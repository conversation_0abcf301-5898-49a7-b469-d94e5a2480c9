import { redirect } from "next/navigation";
import { createClient, getCurrentUser, getUserGroups } from "@/lib/supabase/server";
import { GroupsContent } from "./groups-content";

export default async function GroupsPage() {
  const supabase = await createClient();

  // Check authentication
  const { data: { user: authUser } } = await supabase.auth.getUser();

  if (!authUser) {
    redirect("/auth/login");
  }

  // Get user profile
  const user = await getCurrentUser();

  if (!user) {
    redirect("/auth/login");
  }

  // Fetch user's groups
  const groups = await getUserGroups(user.id);

  return (
    <GroupsContent
      user={user}
      initialGroups={groups}
    />
  );
}
