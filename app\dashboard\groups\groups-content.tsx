"use client";

import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Plus, Users, Lock, Globe, Settings, MoreHorizontal, UserPlus, MessageCircle } from "lucide-react";
import { toast } from "sonner";

import { User, Group } from "@/lib/types";
import { useUserStore } from "@/lib/store";
import { supabaseQueries } from "@/lib/supabase/queries";

import { DashboardLayout } from "@/components/dashboard-layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { LoadingSpinner, EmptyState } from "@/components/loading-states";

interface GroupsContentProps {
  user: User;
  initialGroups: Group[];
}

export function GroupsContent({ user, initialGroups }: GroupsContentProps) {
  const [groups, setGroups] = useState<Group[]>(initialGroups);
  const [isLoading, setIsLoading] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [newGroupName, setNewGroupName] = useState("");
  const [newGroupDescription, setNewGroupDescription] = useState("");
  const [newGroupIsPrivate, setNewGroupIsPrivate] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  const { setCurrentUser, setGroups: setStoreGroups } = useUserStore();

  useEffect(() => {
    setCurrentUser(user);
    setStoreGroups(groups);
  }, [user, groups, setCurrentUser, setStoreGroups]);

  const handleCreateGroup = async () => {
    if (!newGroupName.trim()) {
      toast.error("Please enter a group name");
      return;
    }

    setIsCreating(true);
    try {
      const newGroup = await supabaseQueries.createGroup({
        name: newGroupName,
        description: newGroupDescription,
        is_private: newGroupIsPrivate,
        owner_id: user.id
      });

      setGroups([newGroup, ...groups]);
      toast.success("Group created successfully!");
      
      // Reset form
      setNewGroupName("");
      setNewGroupDescription("");
      setNewGroupIsPrivate(false);
      setIsCreateModalOpen(false);
    } catch (error) {
      console.error("Error creating group:", error);
      toast.error("Failed to create group");
    } finally {
      setIsCreating(false);
    }
  };

  const handleDeleteGroup = async (groupId: string) => {
    try {
      await supabaseQueries.deleteGroup(groupId);
      setGroups(groups.filter(g => g.id !== groupId));
      toast.success("Group deleted successfully");
    } catch (error) {
      console.error("Error deleting group:", error);
      toast.error("Failed to delete group");
    }
  };

  return (
    <DashboardLayout user={user}>
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Groups</h1>
            <p className="text-muted-foreground">
              Create and manage collaborative workspaces
            </p>
          </div>
          
          <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Group
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Group</DialogTitle>
                <DialogDescription>
                  Create a collaborative workspace for your team
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="group-name">Group Name</Label>
                  <Input
                    id="group-name"
                    placeholder="Enter group name"
                    value={newGroupName}
                    onChange={(e) => setNewGroupName(e.target.value)}
                  />
                </div>
                
                <div>
                  <Label htmlFor="group-description">Description (Optional)</Label>
                  <Textarea
                    id="group-description"
                    placeholder="Describe your group's purpose"
                    value={newGroupDescription}
                    onChange={(e) => setNewGroupDescription(e.target.value)}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="private-group"
                    checked={newGroupIsPrivate}
                    onCheckedChange={setNewGroupIsPrivate}
                  />
                  <Label htmlFor="private-group">Private Group</Label>
                </div>
                
                <Button 
                  onClick={handleCreateGroup}
                  disabled={isCreating}
                  className="w-full"
                >
                  {isCreating ? (
                    <LoadingSpinner size="sm" className="mr-2" />
                  ) : (
                    <Plus className="h-4 w-4 mr-2" />
                  )}
                  Create Group
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Groups Grid */}
        {groups.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <AnimatePresence>
              {groups.map((group) => (
                <GroupCard
                  key={group.id}
                  group={group}
                  currentUserId={user.id}
                  onDelete={() => handleDeleteGroup(group.id)}
                />
              ))}
            </AnimatePresence>
          </div>
        ) : (
          <EmptyState
            icon={<Users className="h-12 w-12" />}
            title="No groups yet"
            description="Create your first group to start collaborating with others"
            action={
              <Button onClick={() => setIsCreateModalOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Group
              </Button>
            }
          />
        )}
      </div>
    </DashboardLayout>
  );
}

interface GroupCardProps {
  group: Group;
  currentUserId: string;
  onDelete: () => void;
}

function GroupCard({ group, currentUserId, onDelete }: GroupCardProps) {
  const isOwner = group.owner_id === currentUserId;

  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      transition={{ duration: 0.2 }}
    >
      <Card className="group hover:shadow-lg transition-all duration-200">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={group.avatar_url} />
                <AvatarFallback>
                  {group.name[0]?.toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div>
                <CardTitle className="text-lg">{group.name}</CardTitle>
                <div className="flex items-center space-x-2 mt-1">
                  {group.is_private ? (
                    <Badge variant="secondary" className="text-xs">
                      <Lock className="h-3 w-3 mr-1" />
                      Private
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="text-xs">
                      <Globe className="h-3 w-3 mr-1" />
                      Public
                    </Badge>
                  )}
                  {isOwner && (
                    <Badge variant="default" className="text-xs">
                      Owner
                    </Badge>
                  )}
                </div>
              </div>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100 transition-opacity">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Open Chat
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Invite Members
                </DropdownMenuItem>
                {isOwner && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <Settings className="h-4 w-4 mr-2" />
                      Group Settings
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={onDelete} className="text-destructive">
                      Delete Group
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        
        <CardContent>
          {group.description && (
            <CardDescription className="mb-4">
              {group.description}
            </CardDescription>
          )}
          
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>Created {new Date(group.created_at).toLocaleDateString()}</span>
            <div className="flex items-center space-x-1">
              <Users className="h-4 w-4" />
              <span>0 members</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
