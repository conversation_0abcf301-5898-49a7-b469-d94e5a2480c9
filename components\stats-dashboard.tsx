"use client";

import { useMemo } from "react";
import { motion } from "framer-motion";
import { 
  BarChart3, 
  TrendingUp, 
  HardDrive, 
  Image, 
  Box, 
  Palette,
  Calendar,
  Eye
} from "lucide-react";

import { MediaAsset } from "@/lib/types";
import { AnimatedCounter, AnimatedProgress } from "@/components/animated-counter";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface StatsDashboardProps {
  assets: MediaAsset[];
  className?: string;
}

export function StatsDashboard({ assets, className }: StatsDashboardProps) {
  const stats = useMemo(() => {
    const totalAssets = assets.length;
    const totalSize = assets.reduce((sum, asset) => sum + asset.file_size, 0);
    
    const categoryStats = assets.reduce((acc, asset) => {
      acc[asset.category] = (acc[asset.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const publicAssets = assets.filter(asset => asset.is_public).length;
    const privateAssets = totalAssets - publicAssets;

    const recentAssets = assets.filter(asset => {
      const assetDate = new Date(asset.created_at);
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return assetDate > weekAgo;
    }).length;

    const topTags = assets
      .flatMap(asset => asset.tags)
      .reduce((acc, tag) => {
        acc[tag] = (acc[tag] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

    const sortedTags = Object.entries(topTags)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5);

    return {
      totalAssets,
      totalSize,
      categoryStats,
      publicAssets,
      privateAssets,
      recentAssets,
      topTags: sortedTags,
    };
  }, [assets]);

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const categoryIcons = {
    '2d_art': Image,
    '3d_models': Box,
    'textures_materials': Palette,
  };

  const categoryLabels = {
    '2d_art': '2D Art',
    '3d_models': '3D Models',
    'textures_materials': 'Textures & Materials',
  };

  return (
    <div className={className}>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* Total Assets */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Assets</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <AnimatedCounter value={stats.totalAssets} />
              </div>
              <p className="text-xs text-muted-foreground">
                +{stats.recentAssets} this week
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Storage Used */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Storage Used</CardTitle>
              <HardDrive className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatFileSize(stats.totalSize)}
              </div>
              <AnimatedProgress 
                value={stats.totalSize} 
                max={1024 * 1024 * 1024} // 1GB limit for demo
                className="mt-2"
              />
            </CardContent>
          </Card>
        </motion.div>

        {/* Public Assets */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Public Assets</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <AnimatedCounter value={stats.publicAssets} />
              </div>
              <p className="text-xs text-muted-foreground">
                {stats.privateAssets} private
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">This Week</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <AnimatedCounter value={stats.recentAssets} />
              </div>
              <p className="text-xs text-muted-foreground">
                new uploads
              </p>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Category Breakdown */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Category Breakdown</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {Object.entries(stats.categoryStats).map(([category, count], index) => {
                const Icon = categoryIcons[category as keyof typeof categoryIcons];
                const label = categoryLabels[category as keyof typeof categoryLabels];
                const percentage = (count / stats.totalAssets) * 100;
                
                return (
                  <motion.div
                    key={category}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.6 + index * 0.1 }}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center space-x-3">
                      <Icon className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">{label}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-20 bg-secondary rounded-full h-2">
                        <motion.div
                          className="h-full bg-primary rounded-full"
                          initial={{ width: 0 }}
                          animate={{ width: `${percentage}%` }}
                          transition={{ delay: 0.8 + index * 0.1, duration: 0.6 }}
                        />
                      </div>
                      <span className="text-sm text-muted-foreground w-8">
                        {count}
                      </span>
                    </div>
                  </motion.div>
                );
              })}
            </CardContent>
          </Card>
        </motion.div>

        {/* Top Tags */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Popular Tags</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {stats.topTags.map(([tag, count], index) => (
                  <motion.div
                    key={tag}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.7 + index * 0.1 }}
                    className="flex items-center justify-between"
                  >
                    <Badge variant="secondary" className="text-sm">
                      {tag}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {count} asset{count !== 1 ? 's' : ''}
                    </span>
                  </motion.div>
                ))}
                {stats.topTags.length === 0 && (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    No tags yet. Add tags to your assets to see popular ones here.
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
