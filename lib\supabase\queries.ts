"use client";

import { createClient } from "./client";
import { MediaAsset, MediaCategory, User, Group, ChatMessage, Friendship, GroupMember } from "../types";

// Client-side data fetching functions for real-time updates
export class SupabaseQueries {
  private supabase = createClient();

  // Media Asset queries
  async getUserMediaAssets(userId?: string): Promise<MediaAsset[]> {
    let query = this.supabase
      .from('media_assets')
      .select('*')
      .order('created_at', { ascending: false });

    if (userId) {
      query = query.eq('user_id', userId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching media assets:', error);
      throw error;
    }

    return data || [];
  }

  async getPublicMediaAssets(): Promise<MediaAsset[]> {
    const { data, error } = await this.supabase
      .from('media_assets')
      .select('*')
      .eq('is_public', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching public media assets:', error);
      throw error;
    }

    return data || [];
  }

  async getMediaAssetsByCategory(category: MediaCategory, userId?: string): Promise<MediaAsset[]> {
    let query = this.supabase
      .from('media_assets')
      .select('*')
      .eq('category', category)
      .order('created_at', { ascending: false });

    if (userId) {
      query = query.eq('user_id', userId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching media assets by category:', error);
      throw error;
    }

    return data || [];
  }

  async updateMediaAsset(id: string, updates: Partial<MediaAsset>): Promise<MediaAsset> {
    const { data, error } = await this.supabase
      .from('media_assets')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating media asset:', error);
      throw error;
    }

    return data;
  }

  async deleteMediaAsset(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('media_assets')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting media asset:', error);
      throw error;
    }
  }

  // User queries
  async getCurrentUser(): Promise<User | null> {
    const { data: { user: authUser } } = await this.supabase.auth.getUser();

    if (!authUser) return null;

    const { data, error } = await this.supabase
      .from('users')
      .select('*')
      .eq('id', authUser.id)
      .single();

    if (error) {
      console.error('Error fetching user profile:', error);
      return null;
    }

    return {
      ...data,
      email: authUser.email || '',
    };
  }

  async updateUserProfile(updates: Partial<User>): Promise<User> {
    const { data, error } = await this.supabase
      .from('users')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', updates.id)
      .select()
      .single();

    if (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }

    return {
      ...data,
      email: data.email || '',
    };
  }

  // Group queries
  async getUserGroups(userId: string): Promise<Group[]> {
    const { data, error } = await this.supabase
      .from('groups')
      .select(`
        *,
        group_members!inner(user_id)
      `)
      .eq('group_members.user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching user groups:', error);
      throw error;
    }

    return data || [];
  }

  async createGroup(group: Omit<Group, 'id' | 'created_at' | 'updated_at'>): Promise<Group> {
    const { data, error } = await this.supabase
      .from('groups')
      .insert(group)
      .select()
      .single();

    if (error) throw error;

    // Add the creator as the owner member
    await this.supabase
      .from('group_members')
      .insert({
        group_id: data.id,
        user_id: group.owner_id,
        role: 'owner'
      });

    return data;
  }

  async updateGroup(id: string, updates: Partial<Group>): Promise<Group> {
    const { data, error } = await this.supabase
      .from('groups')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async deleteGroup(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('groups')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  async getGroupMembers(groupId: string): Promise<GroupMember[]> {
    const { data, error } = await this.supabase
      .from('group_members')
      .select(`
        *,
        user:users(*)
      `)
      .eq('group_id', groupId)
      .order('joined_at', { ascending: true });

    if (error) {
      console.error('Error fetching group members:', error);
      throw error;
    }

    return data || [];
  }

  async addGroupMember(groupId: string, userId: string, role: 'admin' | 'member' = 'member'): Promise<GroupMember> {
    const { data, error } = await this.supabase
      .from('group_members')
      .insert({
        group_id: groupId,
        user_id: userId,
        role
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async removeGroupMember(groupId: string, userId: string): Promise<void> {
    const { error } = await this.supabase
      .from('group_members')
      .delete()
      .eq('group_id', groupId)
      .eq('user_id', userId);

    if (error) throw error;
  }

  // Friendship queries
  async getUserFriends(userId: string): Promise<User[]> {
    const { data, error } = await this.supabase
      .from('friendships')
      .select(`
        *,
        requester:users!friendships_requester_id_fkey(*),
        addressee:users!friendships_addressee_id_fkey(*)
      `)
      .or(`requester_id.eq.${userId},addressee_id.eq.${userId}`)
      .eq('status', 'accepted');

    if (error) {
      console.error('Error fetching user friends:', error);
      throw error;
    }

    const friends = data?.map(friendship => {
      return friendship.requester_id === userId
        ? friendship.addressee
        : friendship.requester;
    }) || [];

    return friends;
  }

  async getPendingFriendRequests(userId: string): Promise<Friendship[]> {
    const { data, error } = await this.supabase
      .from('friendships')
      .select(`
        *,
        requester:users!friendships_requester_id_fkey(*),
        addressee:users!friendships_addressee_id_fkey(*)
      `)
      .eq('addressee_id', userId)
      .eq('status', 'pending')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching pending friend requests:', error);
      throw error;
    }

    return data || [];
  }

  async sendFriendRequest(addresseeEmail: string): Promise<Friendship> {
    const { data: { user } } = await this.supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    // First, find the addressee by email
    const { data: addresseeData, error: addresseeError } = await this.supabase
      .from('users')
      .select('id')
      .eq('email', addresseeEmail)
      .single();

    if (addresseeError || !addresseeData) {
      throw new Error('User not found');
    }

    // Check if friendship already exists
    const { data: existingFriendship } = await this.supabase
      .from('friendships')
      .select('*')
      .or(`requester_id.eq.${user.id},addressee_id.eq.${user.id}`)
      .or(`requester_id.eq.${addresseeData.id},addressee_id.eq.${addresseeData.id}`)
      .single();

    if (existingFriendship) {
      throw new Error('Friendship request already exists');
    }

    const { data, error } = await this.supabase
      .from('friendships')
      .insert({
        requester_id: user.id,
        addressee_id: addresseeData.id,
        status: 'pending'
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async acceptFriendRequest(requestId: string): Promise<Friendship> {
    const { data, error } = await this.supabase
      .from('friendships')
      .update({ status: 'accepted', updated_at: new Date().toISOString() })
      .eq('id', requestId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async declineFriendRequest(requestId: string): Promise<void> {
    const { error } = await this.supabase
      .from('friendships')
      .delete()
      .eq('id', requestId);

    if (error) throw error;
  }

  async removeFriend(friendId: string): Promise<void> {
    const { data: { user } } = await this.supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { error } = await this.supabase
      .from('friendships')
      .delete()
      .or(`requester_id.eq.${user.id},addressee_id.eq.${user.id}`)
      .or(`requester_id.eq.${friendId},addressee_id.eq.${friendId}`);

    if (error) throw error;
  }

  // Chat message queries
  async getChatMessages(recipientId?: string, groupId?: string): Promise<ChatMessage[]> {
    const { data: { user } } = await this.supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    let query = this.supabase
      .from('chat_messages')
      .select(`
        *,
        sender:users!chat_messages_sender_id_fkey(*)
      `)
      .order('created_at', { ascending: true });

    if (recipientId) {
      // Direct messages
      query = query.or(`sender_id.eq.${user.id},recipient_id.eq.${user.id}`)
                   .or(`sender_id.eq.${recipientId},recipient_id.eq.${recipientId}`);
    } else if (groupId) {
      // Group messages
      query = query.eq('group_id', groupId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching chat messages:', error);
      throw error;
    }

    return data || [];
  }

  async sendMessage(content: string, recipientId?: string, groupId?: string): Promise<ChatMessage> {
    const { data: { user } } = await this.supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const messageData: any = {
      sender_id: user.id,
      content,
    };

    if (recipientId) {
      messageData.recipient_id = recipientId;
    } else if (groupId) {
      messageData.group_id = groupId;
    } else {
      throw new Error('Either recipientId or groupId must be provided');
    }

    const { data, error } = await this.supabase
      .from('chat_messages')
      .insert(messageData)
      .select(`
        *,
        sender:users!chat_messages_sender_id_fkey(*)
      `)
      .single();

    if (error) throw error;
    return data;
  }

  // Chat queries
  async getChatMessages(recipientId?: string, groupId?: string): Promise<ChatMessage[]> {
    const { data: { user } } = await this.supabase.auth.getUser();
    
    if (!user) throw new Error('User not authenticated');

    let query = this.supabase
      .from('chat_messages')
      .select('*')
      .order('created_at', { ascending: true });

    if (groupId) {
      query = query.eq('group_id', groupId);
    } else if (recipientId) {
      query = query
        .or(`sender_id.eq.${user.id},recipient_id.eq.${user.id}`)
        .or(`sender_id.eq.${recipientId},recipient_id.eq.${recipientId}`);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching chat messages:', error);
      throw error;
    }

    return data || [];
  }

  async sendMessage(content: string, recipientId?: string, groupId?: string): Promise<ChatMessage> {
    const { data: { user } } = await this.supabase.auth.getUser();
    
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await this.supabase
      .from('chat_messages')
      .insert({
        sender_id: user.id,
        recipient_id: recipientId,
        group_id: groupId,
        content,
        message_type: 'text',
      })
      .select()
      .single();

    if (error) {
      console.error('Error sending message:', error);
      throw error;
    }

    return data;
  }

  // Real-time subscriptions
  subscribeToUserAssets(userId: string, callback: (payload: any) => void) {
    return this.supabase
      .channel('user-assets')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'media_assets',
          filter: `user_id=eq.${userId}`,
        },
        callback
      )
      .subscribe();
  }

  subscribeToGroupMessages(groupId: string, callback: (payload: any) => void) {
    return this.supabase
      .channel(`group-${groupId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
          filter: `group_id=eq.${groupId}`,
        },
        callback
      )
      .subscribe();
  }

  subscribeToDirectMessages(userId: string, callback: (payload: any) => void) {
    return this.supabase
      .channel(`user-${userId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
          filter: `recipient_id=eq.${userId}`,
        },
        callback
      )
      .subscribe();
  }
}

// Export singleton instance
export const supabaseQueries = new SupabaseQueries();
