"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Copy, Download, Eye, MoreHorizontal, Tag, Edit } from "lucide-react";
import { toast } from "sonner";
import { MediaAsset } from "@/lib/types";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface MediaCardProps {
  asset: MediaAsset;
  isSelected?: boolean;
  onSelect?: (id: string) => void;
  onPreview?: (asset: MediaAsset) => void;
  onEdit?: (asset: MediaAsset) => void;
  className?: string;
}

const categoryIcons = {
  '2d_art': '🎨',
  '3d_models': '🗿',
  'textures_materials': '🧱',
};

const categoryLabels = {
  '2d_art': '2D Art',
  '3d_models': '3D Models',
  'textures_materials': 'Textures & Materials',
};

export function MediaCard({
  asset,
  isSelected = false,
  onSelect,
  onPreview,
  onEdit,
  className
}: MediaCardProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleCopyName = async () => {
    try {
      await navigator.clipboard.writeText(asset.name);
      toast.success("Asset name copied to clipboard!");
    } catch (error) {
      toast.error("Failed to copy asset name");
    }
  };

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = asset.file_url;
    link.download = asset.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success("Download started!");
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      whileHover={{ y: -4 }}
      transition={{ duration: 0.2 }}
      className={cn(
        "media-card cursor-pointer group",
        isSelected && "ring-2 ring-primary ring-offset-2",
        className
      )}
      onClick={() => onSelect?.(asset.id)}
    >
      {/* Thumbnail */}
      <div className="relative aspect-square overflow-hidden rounded-t-xl bg-muted">
        {!imageError ? (
          <>
            {!imageLoaded && (
              <div className="absolute inset-0 shimmer-loading" />
            )}
            <img
              src={asset.thumbnail_url || asset.file_url}
              alt={asset.name}
              className={cn(
                "h-full w-full object-cover transition-all duration-300 group-hover:scale-105",
                !imageLoaded && "opacity-0"
              )}
              onLoad={() => setImageLoaded(true)}
              onError={() => setImageError(true)}
            />
          </>
        ) : (
          <div className="flex h-full items-center justify-center bg-muted">
            <div className="text-center">
              <div className="text-4xl mb-2">
                {categoryIcons[asset.category]}
              </div>
              <p className="text-sm text-muted-foreground">
                {asset.mime_type}
              </p>
            </div>
          </div>
        )}
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
        
        {/* Quick Actions */}
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="secondary"
                size="icon"
                className="h-8 w-8 bg-white/90 hover:bg-white"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={(e) => {
                e.stopPropagation();
                onPreview?.(asset);
              }}>
                <Eye className="mr-2 h-4 w-4" />
                Preview
              </DropdownMenuItem>
              {onEdit && (
                <DropdownMenuItem onClick={(e) => {
                  e.stopPropagation();
                  onEdit(asset);
                }}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onClick={(e) => {
                e.stopPropagation();
                handleCopyName();
              }}>
                <Copy className="mr-2 h-4 w-4" />
                Copy Name
              </DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => {
                e.stopPropagation();
                handleDownload();
              }}>
                <Download className="mr-2 h-4 w-4" />
                Download
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Category Badge */}
        <div className="absolute top-2 left-2">
          <Badge variant="secondary" className="text-xs bg-white/90">
            {categoryLabels[asset.category]}
          </Badge>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        <div className="space-y-2">
          <h3 className="font-semibold text-sm leading-tight line-clamp-2">
            {asset.name}
          </h3>
          
          {asset.description && (
            <p className="text-xs text-muted-foreground line-clamp-2">
              {asset.description}
            </p>
          )}
          
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>{formatFileSize(asset.file_size)}</span>
            <span>{new Date(asset.created_at).toLocaleDateString()}</span>
          </div>
          
          {/* Tags */}
          {asset.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {asset.tags.slice(0, 3).map((tag) => (
                <Badge
                  key={tag}
                  variant="outline"
                  className="text-xs px-1.5 py-0.5"
                >
                  {tag}
                </Badge>
              ))}
              {asset.tags.length > 3 && (
                <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                  +{asset.tags.length - 3}
                </Badge>
              )}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
}
