import { redirect } from "next/navigation";
import { createClient, getCurrentUser, getUser<PERSON>riends, getUserGroups } from "@/lib/supabase/server";
import { ChatContent } from "./chat-content";

export default async function ChatPage() {
  const supabase = await createClient();

  // Check authentication
  const { data: { user: authUser } } = await supabase.auth.getUser();

  if (!authUser) {
    redirect("/auth/login");
  }

  // Get user profile
  const user = await getCurrentUser();

  if (!user) {
    redirect("/auth/login");
  }

  // Fetch friends and groups for chat
  const [friends, groups] = await Promise.all([
    getUserFriends(user.id),
    getUserGroups(user.id)
  ]);

  return (
    <ChatContent
      user={user}
      friends={friends}
      groups={groups}
    />
  );
}
