import { redirect } from "next/navigation";
import { createClient, getCurrentUser, getUserMediaAssets, getUserFriends, getUserGroups } from "@/lib/supabase/server";
import { ProfileContent } from "./profile-content";

export default async function ProfilePage() {
  const supabase = await createClient();

  // Check authentication
  const { data: { user: authUser } } = await supabase.auth.getUser();

  if (!authUser) {
    redirect("/auth/login");
  }

  // Get user profile
  const user = await getCurrentUser();

  if (!user) {
    redirect("/auth/login");
  }

  // Fetch user's data for profile stats
  const [assets, friends, groups] = await Promise.all([
    getUserMediaAssets(user.id),
    getUserFriends(user.id),
    getUserGroups(user.id)
  ]);

  return (
    <ProfileContent
      user={user}
      assets={assets}
      friends={friends}
      groups={groups}
    />
  );
}
