"use client";

import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  Send, 
  Smile, 
  Paperclip, 
  MoreHorizontal,
  Phone,
  Video,
  Info,
  Search,
  Users,
  MessageCircle
} from "lucide-react";
import { toast } from "sonner";

import { ChatMessage, User, Group } from "@/lib/types";
import { createClient } from "@/lib/supabase/client";
import { useUserStore } from "@/lib/store";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { EmptyState, LoadingSpinner } from "@/components/loading-states";
import { cn } from "@/lib/utils";

interface ChatSystemProps {
  isOpen: boolean;
  onClose: () => void;
}

interface ChatConversation {
  id: string;
  type: 'direct' | 'group';
  name: string;
  avatar?: string;
  lastMessage?: ChatMessage;
  unreadCount: number;
  participants?: User[];
}

export function ChatSystem({ isOpen, onClose }: ChatSystemProps) {
  const { currentUser, friends, groups } = useUserStore();
  const [conversations, setConversations] = useState<ChatConversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<ChatConversation | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const supabase = createClient();

  useEffect(() => {
    if (isOpen) {
      loadConversations();
    }
  }, [isOpen, friends, groups]);

  useEffect(() => {
    if (selectedConversation) {
      loadMessages(selectedConversation);
      subscribeToMessages(selectedConversation);
    }
  }, [selectedConversation]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const loadConversations = () => {
    const directConversations: ChatConversation[] = friends.map(friend => ({
      id: `direct-${friend.id}`,
      type: 'direct' as const,
      name: friend.display_name || friend.email,
      avatar: friend.avatar_url,
      unreadCount: 0,
      participants: [friend],
    }));

    const groupConversations: ChatConversation[] = groups.map(group => ({
      id: `group-${group.id}`,
      type: 'group' as const,
      name: group.name,
      avatar: group.avatar_url,
      unreadCount: 0,
    }));

    setConversations([...directConversations, ...groupConversations]);
  };

  const loadMessages = async (conversation: ChatConversation) => {
    if (!currentUser) return;

    setIsLoading(true);
    try {
      let query = supabase
        .from('chat_messages')
        .select(`
          *,
          sender:sender_id(id, username, display_name, avatar_url, email)
        `)
        .order('created_at', { ascending: true })
        .limit(50);

      if (conversation.type === 'direct') {
        const friendId = conversation.id.replace('direct-', '');
        query = query.or(`and(sender_id.eq.${currentUser.id},recipient_id.eq.${friendId}),and(sender_id.eq.${friendId},recipient_id.eq.${currentUser.id})`);
      } else {
        const groupId = conversation.id.replace('group-', '');
        query = query.eq('group_id', groupId);
      }

      const { data, error } = await query;

      if (error) throw error;

      setMessages(data || []);
    } catch (error) {
      console.error('Error loading messages:', error);
      toast.error("Failed to load messages");
    } finally {
      setIsLoading(false);
    }
  };

  const subscribeToMessages = (conversation: ChatConversation) => {
    if (!currentUser) return;

    let channel;

    if (conversation.type === 'direct') {
      const friendId = conversation.id.replace('direct-', '');
      channel = supabase
        .channel(`direct-${currentUser.id}-${friendId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'chat_messages',
            filter: `or(and(sender_id.eq.${currentUser.id},recipient_id.eq.${friendId}),and(sender_id.eq.${friendId},recipient_id.eq.${currentUser.id}))`,
          },
          (payload) => {
            const newMessage = payload.new as ChatMessage;
            setMessages(prev => [...prev, newMessage]);
          }
        )
        .subscribe();
    } else {
      const groupId = conversation.id.replace('group-', '');
      channel = supabase
        .channel(`group-${groupId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'chat_messages',
            filter: `group_id=eq.${groupId}`,
          },
          (payload) => {
            const newMessage = payload.new as ChatMessage;
            setMessages(prev => [...prev, newMessage]);
          }
        )
        .subscribe();
    }

    return () => {
      if (channel) {
        supabase.removeChannel(channel);
      }
    };
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || !selectedConversation || !currentUser || isSending) return;

    setIsSending(true);
    try {
      const messageData: Partial<ChatMessage> = {
        sender_id: currentUser.id,
        content: newMessage.trim(),
        message_type: 'text',
      };

      if (selectedConversation.type === 'direct') {
        const friendId = selectedConversation.id.replace('direct-', '');
        messageData.recipient_id = friendId;
      } else {
        const groupId = selectedConversation.id.replace('group-', '');
        messageData.group_id = groupId;
      }

      const { error } = await supabase
        .from('chat_messages')
        .insert(messageData);

      if (error) throw error;

      setNewMessage("");
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error("Failed to send message");
    } finally {
      setIsSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const formatMessageTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-5xl max-h-[80vh] overflow-hidden p-0">
        <div className="flex h-[70vh]">
          {/* Conversations Sidebar */}
          <div className="w-80 border-r border-border flex flex-col">
            <DialogHeader className="p-4 border-b">
              <DialogTitle className="flex items-center space-x-2">
                <MessageCircle className="h-5 w-5" />
                <span>Messages</span>
              </DialogTitle>
            </DialogHeader>

            <div className="p-4">
              <Input
                placeholder="Search conversations..."
                className="w-full"
              />
            </div>

            <ScrollArea className="flex-1">
              <div className="space-y-1 p-2">
                {conversations.map((conversation) => (
                  <motion.div
                    key={conversation.id}
                    whileHover={{ x: 4 }}
                    className={cn(
                      "flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors",
                      selectedConversation?.id === conversation.id 
                        ? "bg-secondary" 
                        : "hover:bg-muted/50"
                    )}
                    onClick={() => setSelectedConversation(conversation)}
                  >
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={conversation.avatar} />
                      <AvatarFallback>
                        {conversation.type === 'group' ? (
                          <Users className="h-4 w-4" />
                        ) : (
                          conversation.name[0].toUpperCase()
                        )}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="font-medium truncate">{conversation.name}</p>
                        {conversation.unreadCount > 0 && (
                          <Badge variant="destructive" className="text-xs">
                            {conversation.unreadCount}
                          </Badge>
                        )}
                      </div>
                      {conversation.lastMessage && (
                        <p className="text-sm text-muted-foreground truncate">
                          {conversation.lastMessage.content}
                        </p>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>
            </ScrollArea>
          </div>

          {/* Chat Area */}
          <div className="flex-1 flex flex-col">
            {selectedConversation ? (
              <>
                {/* Chat Header */}
                <div className="p-4 border-b border-border flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarImage src={selectedConversation.avatar} />
                      <AvatarFallback>
                        {selectedConversation.type === 'group' ? (
                          <Users className="h-4 w-4" />
                        ) : (
                          selectedConversation.name[0].toUpperCase()
                        )}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-medium">{selectedConversation.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        {selectedConversation.type === 'group' ? 'Group chat' : 'Direct message'}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="icon">
                      <Phone className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon">
                      <Video className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon">
                      <Info className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Messages */}
                <ScrollArea className="flex-1 p-4">
                  {isLoading ? (
                    <div className="flex justify-center py-8">
                      <LoadingSpinner />
                    </div>
                  ) : messages.length === 0 ? (
                    <EmptyState
                      icon={<MessageCircle className="h-8 w-8" />}
                      title="No messages yet"
                      description="Start the conversation by sending a message."
                    />
                  ) : (
                    <div className="space-y-4">
                      {messages.map((message, index) => {
                        const isOwn = message.sender_id === currentUser?.id;
                        const showAvatar = !isOwn && (
                          index === 0 || 
                          messages[index - 1].sender_id !== message.sender_id
                        );

                        return (
                          <motion.div
                            key={message.id}
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className={cn(
                              "flex items-end space-x-2",
                              isOwn ? "justify-end" : "justify-start"
                            )}
                          >
                            {!isOwn && (
                              <Avatar className={cn("h-6 w-6", !showAvatar && "invisible")}>
                                <AvatarImage src={(message as any).sender?.avatar_url} />
                                <AvatarFallback className="text-xs">
                                  {(message as any).sender?.display_name?.[0] || 
                                   (message as any).sender?.email?.[0].toUpperCase()}
                                </AvatarFallback>
                              </Avatar>
                            )}
                            
                            <div className={cn(
                              "max-w-xs lg:max-w-md px-3 py-2 rounded-lg",
                              isOwn 
                                ? "bg-primary text-primary-foreground" 
                                : "bg-muted"
                            )}>
                              <p className="text-sm">{message.content}</p>
                              <p className={cn(
                                "text-xs mt-1",
                                isOwn ? "text-primary-foreground/70" : "text-muted-foreground"
                              )}>
                                {formatMessageTime(message.created_at)}
                              </p>
                            </div>
                          </motion.div>
                        );
                      })}
                      <div ref={messagesEndRef} />
                    </div>
                  )}
                </ScrollArea>

                {/* Message Input */}
                <div className="p-4 border-t border-border">
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="icon">
                      <Paperclip className="h-4 w-4" />
                    </Button>
                    <Input
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyDown={handleKeyPress}
                      placeholder="Type a message..."
                      className="flex-1"
                      disabled={isSending}
                    />
                    <Button variant="ghost" size="icon">
                      <Smile className="h-4 w-4" />
                    </Button>
                    <Button 
                      onClick={sendMessage} 
                      disabled={!newMessage.trim() || isSending}
                      size="icon"
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </>
            ) : (
              <EmptyState
                icon={<MessageCircle className="h-12 w-12" />}
                title="Select a conversation"
                description="Choose a conversation from the sidebar to start chatting."
              />
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
