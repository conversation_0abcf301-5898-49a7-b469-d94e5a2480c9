import { redirect } from "next/navigation";
import { createClient, getCurrentUser, getUserMediaAssets } from "@/lib/supabase/server";
import { DashboardContent } from "./dashboard-content";

export default async function DashboardPage() {
  const supabase = await createClient();

  // Check authentication
  const { data: { user: authUser } } = await supabase.auth.getUser();

  if (!authUser) {
    redirect("/auth/login");
  }

  // Get user profile
  const user = await getCurrentUser();

  if (!user) {
    redirect("/auth/login");
  }

  // Fetch user's media assets from database
  const assets = await getUserMediaAssets(user.id);

  return (
    <DashboardContent
      user={user}
      initialAssets={assets}
    />
  );
}
