"use client";

import { motion } from "framer-motion";
import { MediaCategory } from "@/lib/types";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface CategoryFilterProps {
  selectedCategory?: MediaCategory;
  onCategoryChange: (category?: MediaCategory) => void;
  counts?: Record<MediaCategory, number>;
  className?: string;
}

const categories = [
  {
    key: '2d_art' as MediaCategory,
    label: '2D Art',
    icon: '🎨',
    description: 'Digital artwork, illustrations, and graphics',
  },
  {
    key: '3d_models' as MediaCategory,
    label: '3D Models',
    icon: '🗿',
    description: 'Three-dimensional models and sculptures',
  },
  {
    key: 'textures_materials' as MediaCategory,
    label: 'Textures & Materials',
    icon: '🧱',
    description: 'Surface textures and material definitions',
  },
];

export function CategoryFilter({
  selectedCategory,
  onCategoryChange,
  counts = {} as Record<MediaCategory, number>,
  className,
}: CategoryFilterProps) {
  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-muted-foreground">Categories</h3>
        {selectedCategory && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onCategoryChange(undefined)}
            className="h-auto p-1 text-xs text-muted-foreground hover:text-foreground"
          >
            Clear
          </Button>
        )}
      </div>

      <div className="space-y-2">
        {/* All Categories */}
        <motion.div
          whileHover={{ x: 4 }}
          transition={{ duration: 0.2 }}
        >
          <Button
            variant={!selectedCategory ? "secondary" : "ghost"}
            className={cn(
              "w-full justify-start h-auto p-3 text-left",
              !selectedCategory && "bg-secondary/80"
            )}
            onClick={() => onCategoryChange(undefined)}
          >
            <div className="flex items-center space-x-3">
              <div className="text-lg">📁</div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <span className="font-medium">All Categories</span>
                  <Badge variant="outline" className="ml-2">
                    {Object.values(counts).reduce((sum, count) => sum + count, 0)}
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  View all media assets
                </p>
              </div>
            </div>
          </Button>
        </motion.div>

        {/* Individual Categories */}
        {categories.map((category) => (
          <motion.div
            key={category.key}
            whileHover={{ x: 4 }}
            transition={{ duration: 0.2 }}
          >
            <Button
              variant={selectedCategory === category.key ? "secondary" : "ghost"}
              className={cn(
                "w-full justify-start h-auto p-3 text-left",
                selectedCategory === category.key && "bg-secondary/80"
              )}
              onClick={() => onCategoryChange(category.key)}
            >
              <div className="flex items-center space-x-3">
                <div className="text-lg">{category.icon}</div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{category.label}</span>
                    <Badge variant="outline" className="ml-2">
                      {counts[category.key] || 0}
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {category.description}
                  </p>
                </div>
              </div>
            </Button>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
