export interface User {
  id: string;
  email: string;
  username?: string;
  avatar_url?: string;
  display_name?: string;
  created_at: string;
  updated_at: string;
}

export interface MediaAsset {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  file_url: string;
  thumbnail_url?: string;
  file_type: 'image' | '3d_model' | 'texture' | 'material';
  file_size: number;
  mime_type: string;
  category: MediaCategory;
  tags: string[];
  metadata?: Record<string, any>;
  is_public: boolean;
  group_id?: string;
  created_at: string;
  updated_at: string;
}

export type MediaCategory = '2d_art' | '3d_models' | 'textures_materials';

export interface Group {
  id: string;
  name: string;
  description?: string;
  avatar_url?: string;
  owner_id: string;
  is_private: boolean;
  created_at: string;
  updated_at: string;
}

export interface GroupMember {
  id: string;
  group_id: string;
  user_id: string;
  role: 'owner' | 'admin' | 'member';
  joined_at: string;
}

export interface Friendship {
  id: string;
  requester_id: string;
  addressee_id: string;
  status: 'pending' | 'accepted' | 'blocked';
  created_at: string;
  updated_at: string;
}

export interface ChatMessage {
  id: string;
  sender_id: string;
  group_id?: string;
  recipient_id?: string;
  content: string;
  message_type: 'text' | 'media' | 'system';
  media_url?: string;
  created_at: string;
  updated_at: string;
}

export interface MediaFilter {
  category?: MediaCategory;
  search?: string;
  tags?: string[];
  user_id?: string;
  group_id?: string;
  minSize?: number;
  maxSize?: number;
  dateFrom?: string;
  dateTo?: string;
  isPublic?: boolean;
  sortBy?: 'created_at' | 'name' | 'file_size' | 'updated_at';
  sortOrder?: 'asc' | 'desc';
}

export interface UploadProgress {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'processing' | 'complete' | 'error';
  error?: string;
}
