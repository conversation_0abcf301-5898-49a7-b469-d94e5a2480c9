"use client";

import { useMemo } from "react";
import { motion } from "framer-motion";
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Image, 
  Folder, 
  Calendar,
  Download,
  Eye,
  Share,
  Heart
} from "lucide-react";

import { User, MediaAsset, Group } from "@/lib/types";
import { DashboardLayout } from "@/components/dashboard-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface AnalyticsContentProps {
  user: User;
  assets: MediaAsset[];
  friends: User[];
  groups: Group[];
}

export function AnalyticsContent({ user, assets, friends, groups }: AnalyticsContentProps) {
  const analytics = useMemo(() => {
    // Calculate analytics data
    const totalAssets = assets.length;
    const totalFriends = friends.length;
    const totalGroups = groups.length;
    
    // Category breakdown
    const categoryStats = assets.reduce((acc, asset) => {
      acc[asset.category] = (acc[asset.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // File size analysis
    const totalSize = assets.reduce((sum, asset) => sum + asset.file_size, 0);
    const avgSize = totalAssets > 0 ? totalSize / totalAssets : 0;

    // Public vs Private
    const publicAssets = assets.filter(a => a.is_public).length;
    const privateAssets = totalAssets - publicAssets;

    // Recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentAssets = assets.filter(
      asset => new Date(asset.created_at) > thirtyDaysAgo
    ).length;

    // Monthly upload trend (mock data for demo)
    const monthlyUploads = [
      { month: 'Jan', uploads: 12 },
      { month: 'Feb', uploads: 19 },
      { month: 'Mar', uploads: 15 },
      { month: 'Apr', uploads: 25 },
      { month: 'May', uploads: 22 },
      { month: 'Jun', uploads: 30 },
    ];

    // Top tags
    const tagCounts = assets.reduce((acc, asset) => {
      asset.tags.forEach(tag => {
        acc[tag] = (acc[tag] || 0) + 1;
      });
      return acc;
    }, {} as Record<string, number>);

    const topTags = Object.entries(tagCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10);

    return {
      totalAssets,
      totalFriends,
      totalGroups,
      categoryStats,
      totalSize,
      avgSize,
      publicAssets,
      privateAssets,
      recentAssets,
      monthlyUploads,
      topTags
    };
  }, [assets, friends, groups]);

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case '2d_art': return '2D Art';
      case '3d_models': return '3D Models';
      case 'textures_materials': return 'Textures & Materials';
      default: return category;
    }
  };

  return (
    <DashboardLayout user={user}>
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold">Analytics</h1>
          <p className="text-muted-foreground">
            Insights into your digital asset portfolio and activity
          </p>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-primary/10 rounded-lg">
                    <Image className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold">{analytics.totalAssets}</p>
                    <p className="text-muted-foreground">Total Assets</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-blue-500/10 rounded-lg">
                    <Users className="h-6 w-6 text-blue-500" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold">{analytics.totalFriends}</p>
                    <p className="text-muted-foreground">Friends</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-green-500/10 rounded-lg">
                    <Folder className="h-6 w-6 text-green-500" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold">{analytics.totalGroups}</p>
                    <p className="text-muted-foreground">Groups</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-orange-500/10 rounded-lg">
                    <TrendingUp className="h-6 w-6 text-orange-500" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold">{analytics.recentAssets}</p>
                    <p className="text-muted-foreground">Recent Uploads</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
            <TabsTrigger value="storage">Storage</TabsTrigger>
            <TabsTrigger value="activity">Activity</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Visibility Breakdown */}
              <Card>
                <CardHeader>
                  <CardTitle>Asset Visibility</CardTitle>
                  <CardDescription>Public vs Private assets</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Public Assets</span>
                      <span>{analytics.publicAssets}</span>
                    </div>
                    <Progress 
                      value={analytics.totalAssets > 0 ? (analytics.publicAssets / analytics.totalAssets) * 100 : 0} 
                      className="h-2"
                    />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Private Assets</span>
                      <span>{analytics.privateAssets}</span>
                    </div>
                    <Progress 
                      value={analytics.totalAssets > 0 ? (analytics.privateAssets / analytics.totalAssets) * 100 : 0} 
                      className="h-2"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Top Tags */}
              <Card>
                <CardHeader>
                  <CardTitle>Popular Tags</CardTitle>
                  <CardDescription>Most used tags in your assets</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {analytics.topTags.length > 0 ? (
                      analytics.topTags.map(([tag, count]) => (
                        <Badge key={tag} variant="secondary" className="text-sm">
                          {tag} ({count})
                        </Badge>
                      ))
                    ) : (
                      <p className="text-muted-foreground">No tags found</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="categories" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Category Breakdown</CardTitle>
                <CardDescription>Distribution of assets by category</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(analytics.categoryStats).map(([category, count]) => (
                  <div key={category} className="space-y-2">
                    <div className="flex justify-between">
                      <span>{getCategoryLabel(category)}</span>
                      <span>{count} assets</span>
                    </div>
                    <Progress 
                      value={analytics.totalAssets > 0 ? (count / analytics.totalAssets) * 100 : 0} 
                      className="h-2"
                    />
                  </div>
                ))}
                {Object.keys(analytics.categoryStats).length === 0 && (
                  <p className="text-center text-muted-foreground py-8">
                    No assets found
                  </p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="storage" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Storage Usage</CardTitle>
                  <CardDescription>Total storage used by your assets</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center">
                    <p className="text-3xl font-bold">{formatFileSize(analytics.totalSize)}</p>
                    <p className="text-muted-foreground">Total Storage Used</p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Average File Size</span>
                      <span>{formatFileSize(analytics.avgSize)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Total Files</span>
                      <span>{analytics.totalAssets}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Storage Insights</CardTitle>
                  <CardDescription>Recommendations for optimization</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                      <span className="text-sm">Storage usage is optimal</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                      <span className="text-sm">Consider organizing with more tags</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-orange-500 rounded-full" />
                      <span className="text-sm">Share more assets publicly</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="activity" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Upload Activity</CardTitle>
                <CardDescription>Your upload activity over the last 6 months</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analytics.monthlyUploads.map((month) => (
                    <div key={month.month} className="space-y-2">
                      <div className="flex justify-between">
                        <span>{month.month}</span>
                        <span>{month.uploads} uploads</span>
                      </div>
                      <Progress 
                        value={(month.uploads / 30) * 100} 
                        className="h-2"
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
