"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Upload, X, File, Image, Box } from "lucide-react";
import { useDropzone } from "react-dropzone";
import { toast } from "sonner";

import { MediaCategory } from "@/lib/types";
import { useUIStore, useMediaStore } from "@/lib/store";
import { fileUploader } from "@/lib/upload";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { ProgressBar } from "@/components/loading-states";
import { cn } from "@/lib/utils";

const categoryOptions = [
  { value: "2d_art", label: "2D Art", icon: Image },
  { value: "3d_models", label: "3D Models", icon: Box },
  { value: "textures_materials", label: "Textures & Materials", icon: File },
];

const acceptedFileTypes = {
  "2d_art": {
    "image/*": [".png", ".jpg", ".jpeg", ".gif", ".webp", ".svg"],
  },
  "3d_models": {
    "model/*": [".obj", ".fbx", ".gltf", ".glb", ".blend", ".3ds", ".dae"],
  },
  "textures_materials": {
    "image/*": [".png", ".jpg", ".jpeg", ".exr", ".hdr", ".tiff"],
  },
};

export function UploadModal() {
  const { uploadModalOpen, setUploadModalOpen } = useUIStore();
  const { addAsset } = useMediaStore();
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [category, setCategory] = useState<MediaCategory>("2d_art");
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState("");
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: acceptedFileTypes[category],
    onDrop: (acceptedFiles) => {
      setSelectedFiles(acceptedFiles);
      if (acceptedFiles.length > 0 && !name) {
        setName(acceptedFiles[0].name.replace(/\.[^/.]+$/, ""));
      }
    },
    multiple: false,
  });

  const handleTagAdd = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput("");
    }
  };

  const handleTagRemove = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleUpload = async () => {
    if (!selectedFiles.length || !name.trim()) {
      toast.error("Please select a file and provide a name");
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const asset = await fileUploader.uploadFile({
        file: selectedFiles[0],
        name: name.trim(),
        description: description.trim() || undefined,
        category,
        tags,
        isPublic: false, // Default to private
        onProgress: setUploadProgress,
      });

      // Add to store
      addAsset(asset);

      toast.success("File uploaded successfully!");

      // Reset form
      setSelectedFiles([]);
      setName("");
      setDescription("");
      setTags([]);
      setUploadModalOpen(false);

    } catch (error) {
      console.error('Upload error:', error);
      toast.error(error instanceof Error ? error.message : "Upload failed. Please try again.");
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleClose = () => {
    if (!isUploading) {
      setUploadModalOpen(false);
      setSelectedFiles([]);
      setName("");
      setDescription("");
      setTags([]);
    }
  };

  return (
    <Dialog open={uploadModalOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>Upload Media Asset</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Category Selection */}
          <div className="space-y-2">
            <Label>Category</Label>
            <Select value={category} onValueChange={(value: MediaCategory) => {
              setCategory(value);
              setSelectedFiles([]); // Clear files when category changes
            }}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {categoryOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center space-x-2">
                      <option.icon className="h-4 w-4" />
                      <span>{option.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* File Upload */}
          <div className="space-y-2">
            <Label>File</Label>
            <div
              {...getRootProps()}
              className={cn(
                "border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors",
                isDragActive ? "border-primary bg-primary/5" : "border-border hover:border-primary/50",
                selectedFiles.length > 0 && "border-green-500 bg-green-50 dark:bg-green-950/20"
              )}
            >
              <input {...getInputProps()} />
              {selectedFiles.length > 0 ? (
                <div className="space-y-2">
                  <File className="h-8 w-8 mx-auto text-green-600" />
                  <p className="font-medium">{selectedFiles[0].name}</p>
                  <p className="text-sm text-muted-foreground">
                    {(selectedFiles[0].size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              ) : (
                <div className="space-y-2">
                  <Upload className="h-8 w-8 mx-auto text-muted-foreground" />
                  <p className="text-muted-foreground">
                    {isDragActive ? "Drop the file here" : "Drag & drop a file here, or click to select"}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Supported formats: {Object.values(acceptedFileTypes[category])[0].join(", ")}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Asset Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Asset name"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="tags">Tags</Label>
              <div className="flex space-x-2">
                <Input
                  id="tags"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  placeholder="Add tag"
                  onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), handleTagAdd())}
                />
                <Button type="button" variant="outline" onClick={handleTagAdd}>
                  Add
                </Button>
              </div>
              {tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 ml-1 hover:bg-destructive hover:text-destructive-foreground"
                        onClick={() => handleTagRemove(tag)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Optional description"
              rows={3}
            />
          </div>

          {/* Upload Progress */}
          {isUploading && (
            <ProgressBar progress={uploadProgress} />
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={handleClose} disabled={isUploading}>
              Cancel
            </Button>
            <Button onClick={handleUpload} disabled={!selectedFiles.length || !name.trim() || isUploading}>
              {isUploading ? "Uploading..." : "Upload"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
