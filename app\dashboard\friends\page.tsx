import { redirect } from "next/navigation";
import { createClient, getCurrentUser, getUser<PERSON>riends, getPendingFriendRequests } from "@/lib/supabase/server";
import { FriendsContent } from "./friends-content";

export default async function FriendsPage() {
  const supabase = await createClient();

  // Check authentication
  const { data: { user: authUser } } = await supabase.auth.getUser();

  if (!authUser) {
    redirect("/auth/login");
  }

  // Get user profile
  const user = await getCurrentUser();

  if (!user) {
    redirect("/auth/login");
  }

  // Fetch friends and pending requests
  const [friends, pendingRequests] = await Promise.all([
    getUserFriends(user.id),
    getPendingFriendRequests(user.id)
  ]);

  return (
    <FriendsContent
      user={user}
      initialFriends={friends}
      initialPendingRequests={pendingRequests}
    />
  );
}
