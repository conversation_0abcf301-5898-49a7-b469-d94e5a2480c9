"use client";

import { useEffect } from "react";
import { toast } from "sonner";
import { useUIStore } from "@/lib/store";

interface KeyboardShortcutsProps {
  onSelectAll?: () => void;
  onClearSelection?: () => void;
  onDelete?: () => void;
  onUpload?: () => void;
}

export function KeyboardShortcuts({
  onSelectAll,
  onClearSelection,
  onDelete,
  onUpload,
}: KeyboardShortcutsProps) {
  const { setUploadModalOpen } = useUIStore();

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in inputs
      if (
        event.target instanceof HTMLInputElement ||
        event.target instanceof HTMLTextAreaElement ||
        event.target instanceof HTMLSelectElement ||
        (event.target as HTMLElement)?.contentEditable === "true"
      ) {
        return;
      }

      const { ctrlKey, metaKey, key, shiftKey } = event;
      const isModifierPressed = ctrlKey || metaKey;

      switch (key.toLowerCase()) {
        case "a":
          if (isModifierPressed) {
            event.preventDefault();
            onSelectAll?.();
            toast.success("All assets selected");
          }
          break;

        case "escape":
          event.preventDefault();
          onClearSelection?.();
          break;

        case "delete":
        case "backspace":
          if (!isModifierPressed) {
            event.preventDefault();
            onDelete?.();
          }
          break;

        case "u":
          if (isModifierPressed) {
            event.preventDefault();
            onUpload?.();
            setUploadModalOpen(true);
          }
          break;

        case "/":
          if (!isModifierPressed) {
            event.preventDefault();
            // Focus search input
            const searchInput = document.querySelector('input[placeholder*="Search"]') as HTMLInputElement;
            searchInput?.focus();
          }
          break;

        case "?":
          if (shiftKey) {
            event.preventDefault();
            showShortcutsHelp();
          }
          break;
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [onSelectAll, onClearSelection, onDelete, onUpload, setUploadModalOpen]);

  const showShortcutsHelp = () => {
    toast.info(
      <div className="space-y-2">
        <div className="font-medium">Keyboard Shortcuts</div>
        <div className="text-sm space-y-1">
          <div><kbd className="px-1 py-0.5 bg-muted rounded text-xs">Ctrl+A</kbd> Select all</div>
          <div><kbd className="px-1 py-0.5 bg-muted rounded text-xs">Esc</kbd> Clear selection</div>
          <div><kbd className="px-1 py-0.5 bg-muted rounded text-xs">Del</kbd> Delete selected</div>
          <div><kbd className="px-1 py-0.5 bg-muted rounded text-xs">Ctrl+U</kbd> Upload</div>
          <div><kbd className="px-1 py-0.5 bg-muted rounded text-xs">/</kbd> Focus search</div>
          <div><kbd className="px-1 py-0.5 bg-muted rounded text-xs">?</kbd> Show shortcuts</div>
        </div>
      </div>,
      { duration: 5000 }
    );
  };

  return null; // This component doesn't render anything
}
