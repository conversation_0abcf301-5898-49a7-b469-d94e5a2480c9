"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Share, Users, UserPlus, Copy, Link, Mail, MessageCircle, X } from "lucide-react";
import { toast } from "sonner";

import { MediaAsset, User, Group } from "@/lib/types";
import { supabaseQueries } from "@/lib/supabase/queries";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { LoadingSpinner } from "@/components/loading-states";

interface MediaSharingProps {
  asset: MediaAsset | null;
  isOpen: boolean;
  onClose: () => void;
}

export function MediaSharing({ asset, isOpen, onClose }: MediaSharingProps) {
  const [friends, setFriends] = useState<User[]>([]);
  const [groups, setGroups] = useState<Group[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [shareEmail, setShareEmail] = useState("");
  const [shareMessage, setShareMessage] = useState("");
  const [selectedFriends, setSelectedFriends] = useState<string[]>([]);
  const [selectedGroups, setSelectedGroups] = useState<string[]>([]);

  useEffect(() => {
    if (isOpen && asset) {
      loadSharingData();
    }
  }, [isOpen, asset]);

  const loadSharingData = async () => {
    setIsLoading(true);
    try {
      const currentUser = await supabaseQueries.getCurrentUser();
      if (currentUser) {
        const [userFriends, userGroups] = await Promise.all([
          supabaseQueries.getUserFriends(currentUser.id),
          supabaseQueries.getUserGroups(currentUser.id)
        ]);
        setFriends(userFriends);
        setGroups(userGroups);
      }
    } catch (error) {
      console.error("Error loading sharing data:", error);
      toast.error("Failed to load sharing options");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyLink = async () => {
    if (!asset) return;
    
    const shareUrl = `${window.location.origin}/shared/${asset.id}`;
    try {
      await navigator.clipboard.writeText(shareUrl);
      toast.success("Share link copied to clipboard!");
    } catch (error) {
      toast.error("Failed to copy link");
    }
  };

  const handleEmailShare = async () => {
    if (!asset || !shareEmail.trim()) {
      toast.error("Please enter an email address");
      return;
    }

    try {
      const shareUrl = `${window.location.origin}/shared/${asset.id}`;
      const subject = `Check out this asset: ${asset.name}`;
      const body = `${shareMessage || `I wanted to share this asset with you: ${asset.name}`}\n\nView it here: ${shareUrl}`;
      
      const mailtoUrl = `mailto:${shareEmail}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
      window.open(mailtoUrl);
      
      toast.success("Email client opened!");
    } catch (error) {
      toast.error("Failed to open email client");
    }
  };

  const handleShareWithFriends = async () => {
    if (!asset || selectedFriends.length === 0) {
      toast.error("Please select friends to share with");
      return;
    }

    try {
      const shareUrl = `${window.location.origin}/shared/${asset.id}`;
      const message = shareMessage || `Check out this asset: ${asset.name} - ${shareUrl}`;
      
      // Send messages to selected friends
      for (const friendId of selectedFriends) {
        await supabaseQueries.sendMessage(message, friendId);
      }
      
      toast.success(`Shared with ${selectedFriends.length} friend(s)!`);
      setSelectedFriends([]);
      setShareMessage("");
    } catch (error) {
      console.error("Error sharing with friends:", error);
      toast.error("Failed to share with friends");
    }
  };

  const handleShareWithGroups = async () => {
    if (!asset || selectedGroups.length === 0) {
      toast.error("Please select groups to share with");
      return;
    }

    try {
      const shareUrl = `${window.location.origin}/shared/${asset.id}`;
      const message = shareMessage || `Check out this asset: ${asset.name} - ${shareUrl}`;
      
      // Send messages to selected groups
      for (const groupId of selectedGroups) {
        await supabaseQueries.sendMessage(message, undefined, groupId);
      }
      
      toast.success(`Shared with ${selectedGroups.length} group(s)!`);
      setSelectedGroups([]);
      setShareMessage("");
    } catch (error) {
      console.error("Error sharing with groups:", error);
      toast.error("Failed to share with groups");
    }
  };

  const toggleFriendSelection = (friendId: string) => {
    setSelectedFriends(prev => 
      prev.includes(friendId) 
        ? prev.filter(id => id !== friendId)
        : [...prev, friendId]
    );
  };

  const toggleGroupSelection = (groupId: string) => {
    setSelectedGroups(prev => 
      prev.includes(groupId) 
        ? prev.filter(id => id !== groupId)
        : [...prev, groupId]
    );
  };

  if (!asset) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Share className="h-5 w-5" />
            <span>Share Asset</span>
          </DialogTitle>
          <DialogDescription>
            Share "{asset.name}" with friends, groups, or via link
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="link" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="link">Link</TabsTrigger>
            <TabsTrigger value="email">Email</TabsTrigger>
            <TabsTrigger value="friends">Friends</TabsTrigger>
            <TabsTrigger value="groups">Groups</TabsTrigger>
          </TabsList>

          <TabsContent value="link" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label>Share Link</Label>
                <div className="flex space-x-2 mt-2">
                  <Input
                    value={`${window.location.origin}/shared/${asset.id}`}
                    readOnly
                    className="flex-1"
                  />
                  <Button onClick={handleCopyLink}>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </Button>
                </div>
              </div>
              <p className="text-sm text-muted-foreground">
                Anyone with this link can view the asset if it's public
              </p>
            </div>
          </TabsContent>

          <TabsContent value="email" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="share-email">Email Address</Label>
                <Input
                  id="share-email"
                  type="email"
                  placeholder="Enter email address"
                  value={shareEmail}
                  onChange={(e) => setShareEmail(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="share-message">Message (Optional)</Label>
                <Input
                  id="share-message"
                  placeholder="Add a personal message"
                  value={shareMessage}
                  onChange={(e) => setShareMessage(e.target.value)}
                />
              </div>
              <Button onClick={handleEmailShare} className="w-full">
                <Mail className="h-4 w-4 mr-2" />
                Send Email
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="friends" className="space-y-4">
            {isLoading ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner />
              </div>
            ) : (
              <div className="space-y-4">
                <div>
                  <Label>Select Friends</Label>
                  <div className="max-h-48 overflow-y-auto border rounded-lg p-2 mt-2">
                    {friends.length > 0 ? (
                      friends.map((friend) => (
                        <div
                          key={friend.id}
                          className={`flex items-center space-x-3 p-2 rounded cursor-pointer hover:bg-muted ${
                            selectedFriends.includes(friend.id) ? 'bg-primary/10' : ''
                          }`}
                          onClick={() => toggleFriendSelection(friend.id)}
                        >
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={friend.avatar_url} />
                            <AvatarFallback>
                              {friend.display_name?.[0] || friend.username?.[0] || friend.email[0]}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <p className="text-sm font-medium">
                              {friend.display_name || friend.username || "Anonymous"}
                            </p>
                            <p className="text-xs text-muted-foreground">{friend.email}</p>
                          </div>
                          {selectedFriends.includes(friend.id) && (
                            <Badge variant="default" className="text-xs">Selected</Badge>
                          )}
                        </div>
                      ))
                    ) : (
                      <p className="text-center text-muted-foreground py-4">No friends found</p>
                    )}
                  </div>
                </div>
                
                {selectedFriends.length > 0 && (
                  <>
                    <div>
                      <Label htmlFor="friend-message">Message (Optional)</Label>
                      <Input
                        id="friend-message"
                        placeholder="Add a personal message"
                        value={shareMessage}
                        onChange={(e) => setShareMessage(e.target.value)}
                      />
                    </div>
                    <Button onClick={handleShareWithFriends} className="w-full">
                      <MessageCircle className="h-4 w-4 mr-2" />
                      Share with {selectedFriends.length} friend(s)
                    </Button>
                  </>
                )}
              </div>
            )}
          </TabsContent>

          <TabsContent value="groups" className="space-y-4">
            {isLoading ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner />
              </div>
            ) : (
              <div className="space-y-4">
                <div>
                  <Label>Select Groups</Label>
                  <div className="max-h-48 overflow-y-auto border rounded-lg p-2 mt-2">
                    {groups.length > 0 ? (
                      groups.map((group) => (
                        <div
                          key={group.id}
                          className={`flex items-center space-x-3 p-2 rounded cursor-pointer hover:bg-muted ${
                            selectedGroups.includes(group.id) ? 'bg-primary/10' : ''
                          }`}
                          onClick={() => toggleGroupSelection(group.id)}
                        >
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={group.avatar_url} />
                            <AvatarFallback>{group.name[0]?.toUpperCase()}</AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <p className="text-sm font-medium">{group.name}</p>
                            <p className="text-xs text-muted-foreground">
                              {group.is_private ? "Private" : "Public"} Group
                            </p>
                          </div>
                          {selectedGroups.includes(group.id) && (
                            <Badge variant="default" className="text-xs">Selected</Badge>
                          )}
                        </div>
                      ))
                    ) : (
                      <p className="text-center text-muted-foreground py-4">No groups found</p>
                    )}
                  </div>
                </div>
                
                {selectedGroups.length > 0 && (
                  <>
                    <div>
                      <Label htmlFor="group-message">Message (Optional)</Label>
                      <Input
                        id="group-message"
                        placeholder="Add a personal message"
                        value={shareMessage}
                        onChange={(e) => setShareMessage(e.target.value)}
                      />
                    </div>
                    <Button onClick={handleShareWithGroups} className="w-full">
                      <MessageCircle className="h-4 w-4 mr-2" />
                      Share with {selectedGroups.length} group(s)
                    </Button>
                  </>
                )}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
