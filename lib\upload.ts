import { createClient } from "./supabase/client";
import { MediaAsset, MediaCategory, UploadProgress } from "./types";

export interface UploadOptions {
  file: File;
  name: string;
  description?: string;
  category: MediaCategory;
  tags: string[];
  isPublic?: boolean;
  groupId?: string;
  onProgress?: (progress: number) => void;
}

export class FileUploader {
  private supabase = createClient();

  async uploadFile(options: UploadOptions): Promise<MediaAsset> {
    const { file, name, description, category, tags, isPublic = false, groupId, onProgress } = options;

    try {
      // Generate unique file path
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `${category}/${fileName}`;

      // Upload file to Supabase Storage
      onProgress?.(10);
      
      const { data: uploadData, error: uploadError } = await this.supabase.storage
        .from('media-assets')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        throw new Error(`Upload failed: ${uploadError.message}`);
      }

      onProgress?.(50);

      // Get public URL
      const { data: { publicUrl } } = this.supabase.storage
        .from('media-assets')
        .getPublicUrl(filePath);

      onProgress?.(70);

      // Generate thumbnail if it's an image
      let thumbnailUrl = publicUrl;
      if (file.type.startsWith('image/')) {
        thumbnailUrl = await this.generateThumbnail(file, filePath);
      }

      onProgress?.(80);

      // Get current user
      const { data: { user } } = await this.supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Create media asset record
      const assetData = {
        user_id: user.id,
        name,
        description,
        file_url: publicUrl,
        thumbnail_url: thumbnailUrl,
        file_type: this.getFileType(file.type, category),
        file_size: file.size,
        mime_type: file.type,
        category,
        tags,
        metadata: await this.extractMetadata(file),
        is_public: isPublic,
        group_id: groupId,
      };

      const { data: asset, error: dbError } = await this.supabase
        .from('media_assets')
        .insert(assetData)
        .select()
        .single();

      if (dbError) {
        // Clean up uploaded file if database insert fails
        await this.supabase.storage
          .from('media-assets')
          .remove([filePath]);
        throw new Error(`Database error: ${dbError.message}`);
      }

      onProgress?.(100);
      return asset;

    } catch (error) {
      throw error;
    }
  }

  private async generateThumbnail(file: File, originalPath: string): Promise<string> {
    try {
      // Create canvas for thumbnail generation
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      return new Promise((resolve, reject) => {
        img.onload = async () => {
          // Calculate thumbnail dimensions (max 400px)
          const maxSize = 400;
          let { width, height } = img;
          
          if (width > height) {
            if (width > maxSize) {
              height = (height * maxSize) / width;
              width = maxSize;
            }
          } else {
            if (height > maxSize) {
              width = (width * maxSize) / height;
              height = maxSize;
            }
          }

          canvas.width = width;
          canvas.height = height;
          
          ctx?.drawImage(img, 0, 0, width, height);
          
          canvas.toBlob(async (blob) => {
            if (!blob) {
              resolve(this.supabase.storage.from('media-assets').getPublicUrl(originalPath).data.publicUrl);
              return;
            }

            const thumbnailPath = originalPath.replace(/(\.[^.]+)$/, '_thumb$1');
            
            const { error } = await this.supabase.storage
              .from('media-assets')
              .upload(thumbnailPath, blob);

            if (error) {
              resolve(this.supabase.storage.from('media-assets').getPublicUrl(originalPath).data.publicUrl);
              return;
            }

            const { data: { publicUrl } } = this.supabase.storage
              .from('media-assets')
              .getPublicUrl(thumbnailPath);
            
            resolve(publicUrl);
          }, 'image/jpeg', 0.8);
        };

        img.onerror = () => {
          resolve(this.supabase.storage.from('media-assets').getPublicUrl(originalPath).data.publicUrl);
        };

        img.src = URL.createObjectURL(file);
      });
    } catch (error) {
      // Return original URL if thumbnail generation fails
      return this.supabase.storage.from('media-assets').getPublicUrl(originalPath).data.publicUrl;
    }
  }

  private getFileType(mimeType: string, category: MediaCategory): 'image' | '3d_model' | 'texture' | 'material' {
    if (mimeType.startsWith('image/')) {
      return category === 'textures_materials' ? 'texture' : 'image';
    }
    if (mimeType.startsWith('model/') || mimeType.includes('gltf') || mimeType.includes('obj')) {
      return '3d_model';
    }
    return 'material';
  }

  private async extractMetadata(file: File): Promise<Record<string, any>> {
    const metadata: Record<string, any> = {
      originalName: file.name,
      lastModified: file.lastModified,
    };

    if (file.type.startsWith('image/')) {
      try {
        const img = new Image();
        const dimensions = await new Promise<{ width: number; height: number }>((resolve) => {
          img.onload = () => resolve({ width: img.width, height: img.height });
          img.onerror = () => resolve({ width: 0, height: 0 });
          img.src = URL.createObjectURL(file);
        });
        
        metadata.width = dimensions.width;
        metadata.height = dimensions.height;
        metadata.aspectRatio = dimensions.width / dimensions.height;
      } catch (error) {
        // Ignore metadata extraction errors
      }
    }

    return metadata;
  }

  async deleteAsset(assetId: string): Promise<void> {
    const { data: asset, error: fetchError } = await this.supabase
      .from('media_assets')
      .select('file_url, thumbnail_url')
      .eq('id', assetId)
      .single();

    if (fetchError || !asset) {
      throw new Error('Asset not found');
    }

    // Extract file paths from URLs
    const filePath = this.extractPathFromUrl(asset.file_url);
    const thumbnailPath = asset.thumbnail_url !== asset.file_url 
      ? this.extractPathFromUrl(asset.thumbnail_url) 
      : null;

    // Delete from storage
    const filesToDelete = [filePath];
    if (thumbnailPath) filesToDelete.push(thumbnailPath);

    await this.supabase.storage
      .from('media-assets')
      .remove(filesToDelete);

    // Delete from database
    const { error: deleteError } = await this.supabase
      .from('media_assets')
      .delete()
      .eq('id', assetId);

    if (deleteError) {
      throw new Error(`Failed to delete asset: ${deleteError.message}`);
    }
  }

  private extractPathFromUrl(url: string): string {
    const urlParts = url.split('/');
    const bucketIndex = urlParts.findIndex(part => part === 'media-assets');
    return urlParts.slice(bucketIndex + 1).join('/');
  }
}

export const fileUploader = new FileUploader();
