-- Create storage bucket for media assets
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'media-assets',
  'media-assets',
  true,
  52428800, -- 50MB limit
  ARRAY[
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml',
    'image/tiff',
    'image/bmp',
    'model/gltf+json',
    'model/gltf-binary',
    'model/obj',
    'model/fbx',
    'application/octet-stream', -- For various 3D formats
    'text/plain' -- For some 3D formats like .obj
  ]
) ON CONFLICT (id) DO NOTHING;

-- Storage policies for media-assets bucket
CREATE POLICY "Users can upload their own media assets" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'media-assets' AND 
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can view public media assets" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'media-assets' AND (
      -- Public assets (check if corresponding media_asset record is public)
      EXISTS (
        SELECT 1 FROM public.media_assets 
        WHERE file_url LIKE '%' || name || '%' AND is_public = true
      ) OR
      -- User's own assets
      auth.uid()::text = (storage.foldername(name))[1] OR
      -- Assets in groups user is member of
      EXISTS (
        SELECT 1 FROM public.media_assets ma
        JOIN public.group_members gm ON ma.group_id = gm.group_id
        WHERE ma.file_url LIKE '%' || name || '%' 
        AND gm.user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can update their own media assets" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'media-assets' AND 
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own media assets" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'media-assets' AND 
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Function to automatically create user folder structure
CREATE OR REPLACE FUNCTION create_user_storage_folder()
RETURNS TRIGGER AS $$
BEGIN
  -- Create user folder in storage (this will be done automatically when first file is uploaded)
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create user folder on user creation
CREATE TRIGGER on_user_created_storage
  AFTER INSERT ON public.users
  FOR EACH ROW EXECUTE FUNCTION create_user_storage_folder();
