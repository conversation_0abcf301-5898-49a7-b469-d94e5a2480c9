"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { 
  Download, 
  Trash2, 
  Tag, 
  Share, 
  Archive,
  X,
  Check,
  Loader2
} from "lucide-react";
import { toast } from "sonner";

import { MediaAsset } from "@/lib/types";
import { fileUploader } from "@/lib/upload";
import { useMediaStore } from "@/lib/store";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

interface BulkOperationsProps {
  selectedAssets: string[];
  assets: MediaAsset[];
  onClearSelection: () => void;
}

export function BulkOperations({ selectedAssets, assets, onClearSelection }: BulkOperationsProps) {
  const [isTagModalOpen, setIsTagModalOpen] = useState(false);
  const [newTags, setNewTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const { removeAsset, updateAsset } = useMediaStore();

  const selectedAssetsData = assets.filter(asset => selectedAssets.includes(asset.id));

  if (selectedAssets.length === 0) return null;

  const handleBulkDownload = async () => {
    setIsProcessing(true);
    try {
      for (const asset of selectedAssetsData) {
        const link = document.createElement('a');
        link.href = asset.file_url;
        link.download = asset.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Small delay to prevent overwhelming the browser
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      toast.success(`Downloaded ${selectedAssets.length} assets`);
      onClearSelection();
    } catch (error) {
      toast.error("Failed to download some assets");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBulkDelete = async () => {
    if (!confirm(`Are you sure you want to delete ${selectedAssets.length} assets? This action cannot be undone.`)) {
      return;
    }

    setIsProcessing(true);
    try {
      const deletePromises = selectedAssets.map(assetId => 
        fileUploader.deleteAsset(assetId).then(() => removeAsset(assetId))
      );
      
      await Promise.all(deletePromises);
      
      toast.success(`Deleted ${selectedAssets.length} assets`);
      onClearSelection();
    } catch (error) {
      toast.error("Failed to delete some assets");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !newTags.includes(tagInput.trim())) {
      setNewTags([...newTags, tagInput.trim()]);
      setTagInput("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setNewTags(newTags.filter(tag => tag !== tagToRemove));
  };

  const handleBulkTagUpdate = async () => {
    if (newTags.length === 0) return;

    setIsProcessing(true);
    try {
      const updatePromises = selectedAssets.map(assetId => {
        const asset = assets.find(a => a.id === assetId);
        if (!asset) return Promise.resolve();

        const updatedTags = Array.from(new Set([...asset.tags, ...newTags]));
        return updateAsset(assetId, { tags: updatedTags });
      });

      await Promise.all(updatePromises);
      
      toast.success(`Added tags to ${selectedAssets.length} assets`);
      setIsTagModalOpen(false);
      setNewTags([]);
      onClearSelection();
    } catch (error) {
      toast.error("Failed to update tags");
    } finally {
      setIsProcessing(false);
    }
  };

  const totalSize = selectedAssetsData.reduce((sum, asset) => sum + asset.file_size, 0);
  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 50 }}
        className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50"
      >
        <div className="bg-card border border-border rounded-lg shadow-premium p-4 min-w-96">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                <Check className="h-3 w-3 text-primary-foreground" />
              </div>
              <span className="font-medium">
                {selectedAssets.length} asset{selectedAssets.length !== 1 ? 's' : ''} selected
              </span>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClearSelection}
              className="h-6 w-6"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>

          <div className="text-xs text-muted-foreground mb-4">
            Total size: {formatFileSize(totalSize)}
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleBulkDownload}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
              ) : (
                <Download className="h-3 w-3 mr-1" />
              )}
              Download
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsTagModalOpen(true)}
              disabled={isProcessing}
            >
              <Tag className="h-3 w-3 mr-1" />
              Add Tags
            </Button>

            <Button
              variant="outline"
              size="sm"
              disabled={isProcessing}
            >
              <Share className="h-3 w-3 mr-1" />
              Share
            </Button>

            <Separator orientation="vertical" className="h-6" />

            <Button
              variant="destructive"
              size="sm"
              onClick={handleBulkDelete}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
              ) : (
                <Trash2 className="h-3 w-3 mr-1" />
              )}
              Delete
            </Button>
          </div>
        </div>
      </motion.div>

      {/* Tag Modal */}
      <Dialog open={isTagModalOpen} onOpenChange={setIsTagModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add Tags to Selected Assets</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Add new tags</Label>
              <div className="flex space-x-2">
                <Input
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  placeholder="Enter tag name"
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
                />
                <Button onClick={handleAddTag} disabled={!tagInput.trim()}>
                  Add
                </Button>
              </div>
            </div>

            {newTags.length > 0 && (
              <div className="space-y-2">
                <Label>Tags to add:</Label>
                <div className="flex flex-wrap gap-1">
                  {newTags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 ml-1 hover:bg-destructive hover:text-destructive-foreground"
                        onClick={() => handleRemoveTag(tag)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            <div className="text-sm text-muted-foreground">
              These tags will be added to all {selectedAssets.length} selected assets.
            </div>

            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setIsTagModalOpen(false)}
                disabled={isProcessing}
              >
                Cancel
              </Button>
              <Button
                onClick={handleBulkTagUpdate}
                disabled={newTags.length === 0 || isProcessing}
              >
                {isProcessing ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : null}
                Add Tags
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
