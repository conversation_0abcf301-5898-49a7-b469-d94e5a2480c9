"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  Plus, 
  Users, 
  Settings, 
  UserPlus, 
  Crown,
  Shield,
  User,
  MoreHorizontal,
  Edit,
  Trash2
} from "lucide-react";
import { toast } from "sonner";

import { Group, GroupMember, User as UserType } from "@/lib/types";
import { createClient } from "@/lib/supabase/client";
import { useUserStore } from "@/lib/store";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Switch } from "@/components/ui/switch";
import { EmptyState, LoadingSpinner } from "@/components/loading-states";

interface GroupsManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

export function GroupsManager({ isOpen, onClose }: GroupsManagerProps) {
  const { currentUser, groups, setGroups, addGroup, updateGroup, removeGroup } = useUserStore();
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);
  const [groupMembers, setGroupMembers] = useState<(GroupMember & { user: UserType })[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [newGroup, setNewGroup] = useState({
    name: "",
    description: "",
    is_private: false,
  });

  const supabase = createClient();

  useEffect(() => {
    if (isOpen) {
      loadGroups();
    }
  }, [isOpen]);

  const loadGroups = async () => {
    if (!currentUser) return;

    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('groups')
        .select(`
          *,
          group_members!inner(user_id, role)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      setGroups(data || []);
    } catch (error) {
      console.error('Error loading groups:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadGroupMembers = async (groupId: string) => {
    try {
      const { data, error } = await supabase
        .from('group_members')
        .select(`
          *,
          user:user_id(id, username, display_name, avatar_url, email)
        `)
        .eq('group_id', groupId);

      if (error) throw error;

      setGroupMembers(data || []);
    } catch (error) {
      console.error('Error loading group members:', error);
    }
  };

  const createGroup = async () => {
    if (!currentUser || !newGroup.name.trim()) return;

    try {
      const { data: group, error: groupError } = await supabase
        .from('groups')
        .insert({
          name: newGroup.name.trim(),
          description: newGroup.description.trim() || null,
          is_private: newGroup.is_private,
          owner_id: currentUser.id,
        })
        .select()
        .single();

      if (groupError) throw groupError;

      // Add creator as owner member
      const { error: memberError } = await supabase
        .from('group_members')
        .insert({
          group_id: group.id,
          user_id: currentUser.id,
          role: 'owner',
        });

      if (memberError) throw memberError;

      addGroup(group);
      setNewGroup({ name: "", description: "", is_private: false });
      setIsCreating(false);
      toast.success("Group created successfully!");
    } catch (error) {
      toast.error("Failed to create group");
      console.error('Group creation error:', error);
    }
  };

  const deleteGroup = async (groupId: string) => {
    if (!confirm("Are you sure you want to delete this group? This action cannot be undone.")) {
      return;
    }

    try {
      const { error } = await supabase
        .from('groups')
        .delete()
        .eq('id', groupId);

      if (error) throw error;

      removeGroup(groupId);
      setSelectedGroup(null);
      toast.success("Group deleted successfully!");
    } catch (error) {
      toast.error("Failed to delete group");
      console.error('Group deletion error:', error);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner':
        return <Crown className="h-4 w-4 text-yellow-500" />;
      case 'admin':
        return <Shield className="h-4 w-4 text-blue-500" />;
      default:
        return <User className="h-4 w-4 text-gray-500" />;
    }
  };

  const getRoleBadge = (role: string) => {
    const variants = {
      owner: "default",
      admin: "secondary",
      member: "outline",
    } as const;

    return (
      <Badge variant={variants[role as keyof typeof variants] || "outline"}>
        {role}
      </Badge>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>Groups</span>
            </DialogTitle>
            <Button onClick={() => setIsCreating(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Group
            </Button>
          </div>
        </DialogHeader>

        {isCreating && (
          <div className="space-y-4 p-4 border rounded-lg">
            <h3 className="font-medium">Create New Group</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="group-name">Group Name *</Label>
                <Input
                  id="group-name"
                  value={newGroup.name}
                  onChange={(e) => setNewGroup(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter group name"
                />
              </div>
              <div className="space-y-2">
                <Label className="flex items-center space-x-2">
                  <span>Private Group</span>
                  <Switch
                    checked={newGroup.is_private}
                    onCheckedChange={(checked) => setNewGroup(prev => ({ ...prev, is_private: checked }))}
                  />
                </Label>
                <p className="text-xs text-muted-foreground">
                  Private groups are only visible to members
                </p>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="group-description">Description</Label>
              <Textarea
                id="group-description"
                value={newGroup.description}
                onChange={(e) => setNewGroup(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Optional group description"
                rows={3}
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsCreating(false)}>
                Cancel
              </Button>
              <Button onClick={createGroup} disabled={!newGroup.name.trim()}>
                Create Group
              </Button>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 flex-1 overflow-hidden">
          {/* Groups List */}
          <div className="space-y-4">
            <h3 className="font-medium">Your Groups ({groups.length})</h3>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : groups.length === 0 ? (
                <EmptyState
                  icon={<Users className="h-8 w-8" />}
                  title="No groups yet"
                  description="Create or join groups to collaborate with others."
                />
              ) : (
                groups.map((group) => (
                  <motion.div
                    key={group.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedGroup?.id === group.id ? 'bg-secondary' : 'hover:bg-muted/50'
                    }`}
                    onClick={() => {
                      setSelectedGroup(group);
                      loadGroupMembers(group.id);
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium truncate">{group.name}</h4>
                          {group.is_private && (
                            <Badge variant="outline" className="text-xs">
                              Private
                            </Badge>
                          )}
                        </div>
                        {group.description && (
                          <p className="text-sm text-muted-foreground truncate">
                            {group.description}
                          </p>
                        )}
                      </div>
                      
                      {group.owner_id === currentUser?.id && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Group
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => deleteGroup(group.id)}
                              className="text-destructive"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete Group
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>
                  </motion.div>
                ))
              )}
            </div>
          </div>

          {/* Group Details */}
          <div className="space-y-4">
            {selectedGroup ? (
              <>
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">Group Members</h3>
                  <Button size="sm" variant="outline">
                    <UserPlus className="h-4 w-4 mr-2" />
                    Invite
                  </Button>
                </div>
                
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {groupMembers.map((member) => (
                    <motion.div
                      key={member.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="flex items-center justify-between p-3 rounded-lg border"
                    >
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={member.user.avatar_url} />
                          <AvatarFallback className="text-xs">
                            {member.user.display_name?.[0] || member.user.email[0].toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium text-sm">
                            {member.user.display_name || member.user.email}
                          </p>
                          {member.user.username && (
                            <p className="text-xs text-muted-foreground">
                              @{member.user.username}
                            </p>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {getRoleIcon(member.role)}
                        {getRoleBadge(member.role)}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </>
            ) : (
              <EmptyState
                icon={<Users className="h-8 w-8" />}
                title="Select a group"
                description="Choose a group from the list to view its members and settings."
              />
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
