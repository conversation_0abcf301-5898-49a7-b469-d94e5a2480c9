import { create } from 'zustand';
import { MediaAsset, MediaCategory, MediaFilter, User, Group } from './types';

interface MediaStore {
  assets: MediaAsset[];
  filteredAssets: MediaAsset[];
  currentFilter: MediaFilter;
  isLoading: boolean;
  selectedAssets: string[];
  
  setAssets: (assets: MediaAsset[]) => void;
  addAsset: (asset: MediaAsset) => void;
  updateAsset: (id: string, updates: Partial<MediaAsset>) => void;
  removeAsset: (id: string) => void;
  setFilter: (filter: MediaFilter) => void;
  setLoading: (loading: boolean) => void;
  toggleAssetSelection: (id: string) => void;
  clearSelection: () => void;
  applyFilter: () => void;
}

export const useMediaStore = create<MediaStore>((set, get) => ({
  assets: [],
  filteredAssets: [],
  currentFilter: {},
  isLoading: false,
  selectedAssets: [],

  setAssets: (assets) => {
    set({ assets });
    get().applyFilter();
  },

  addAsset: (asset) => {
    set((state) => ({ assets: [asset, ...state.assets] }));
    get().applyFilter();
  },

  updateAsset: (id, updates) => {
    set((state) => ({
      assets: state.assets.map((asset) =>
        asset.id === id ? { ...asset, ...updates } : asset
      ),
    }));
    get().applyFilter();
  },

  removeAsset: (id) => {
    set((state) => ({
      assets: state.assets.filter((asset) => asset.id !== id),
      selectedAssets: state.selectedAssets.filter((assetId) => assetId !== id),
    }));
    get().applyFilter();
  },

  setFilter: (filter) => {
    set({ currentFilter: filter });
    get().applyFilter();
  },

  setLoading: (isLoading) => set({ isLoading }),

  toggleAssetSelection: (id) => {
    set((state) => ({
      selectedAssets: state.selectedAssets.includes(id)
        ? state.selectedAssets.filter((assetId) => assetId !== id)
        : [...state.selectedAssets, id],
    }));
  },

  clearSelection: () => set({ selectedAssets: [] }),

  applyFilter: () => {
    const { assets, currentFilter } = get();
    let filtered = [...assets];

    if (currentFilter.category) {
      filtered = filtered.filter((asset) => asset.category === currentFilter.category);
    }

    if (currentFilter.search) {
      const searchLower = currentFilter.search.toLowerCase();
      filtered = filtered.filter(
        (asset) =>
          asset.name.toLowerCase().includes(searchLower) ||
          asset.description?.toLowerCase().includes(searchLower) ||
          asset.tags.some((tag) => tag.toLowerCase().includes(searchLower))
      );
    }

    if (currentFilter.tags && currentFilter.tags.length > 0) {
      filtered = filtered.filter((asset) =>
        currentFilter.tags!.some((tag) => asset.tags.includes(tag))
      );
    }

    if (currentFilter.user_id) {
      filtered = filtered.filter((asset) => asset.user_id === currentFilter.user_id);
    }

    if (currentFilter.group_id) {
      filtered = filtered.filter((asset) => asset.group_id === currentFilter.group_id);
    }

    set({ filteredAssets: filtered });
  },
}));

interface UserStore {
  currentUser: User | null;
  friends: User[];
  groups: Group[];
  
  setCurrentUser: (user: User | null) => void;
  setFriends: (friends: User[]) => void;
  addFriend: (friend: User) => void;
  removeFriend: (friendId: string) => void;
  setGroups: (groups: Group[]) => void;
  addGroup: (group: Group) => void;
  updateGroup: (id: string, updates: Partial<Group>) => void;
  removeGroup: (id: string) => void;
}

export const useUserStore = create<UserStore>((set) => ({
  currentUser: null,
  friends: [],
  groups: [],

  setCurrentUser: (currentUser) => set({ currentUser }),
  
  setFriends: (friends) => set({ friends }),
  
  addFriend: (friend) =>
    set((state) => ({ friends: [...state.friends, friend] })),
  
  removeFriend: (friendId) =>
    set((state) => ({
      friends: state.friends.filter((friend) => friend.id !== friendId),
    })),
  
  setGroups: (groups) => set({ groups }),
  
  addGroup: (group) =>
    set((state) => ({ groups: [group, ...state.groups] })),
  
  updateGroup: (id, updates) =>
    set((state) => ({
      groups: state.groups.map((group) =>
        group.id === id ? { ...group, ...updates } : group
      ),
    })),
  
  removeGroup: (id) =>
    set((state) => ({
      groups: state.groups.filter((group) => group.id !== id),
    })),
}));

interface UIStore {
  sidebarOpen: boolean;
  uploadModalOpen: boolean;
  selectedMediaId: string | null;
  
  setSidebarOpen: (open: boolean) => void;
  setUploadModalOpen: (open: boolean) => void;
  setSelectedMediaId: (id: string | null) => void;
}

export const useUIStore = create<UIStore>((set) => ({
  sidebarOpen: false,
  uploadModalOpen: false,
  selectedMediaId: null,

  setSidebarOpen: (sidebarOpen) => set({ sidebarOpen }),
  setUploadModalOpen: (uploadModalOpen) => set({ uploadModalOpen }),
  setSelectedMediaId: (selectedMediaId) => set({ selectedMediaId }),
}));
