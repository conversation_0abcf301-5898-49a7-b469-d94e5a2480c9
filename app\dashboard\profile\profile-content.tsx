"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Camera, Edit, Save, X, Users, Image, Folder, Calendar, Mail, User as UserIcon } from "lucide-react";
import { toast } from "sonner";

import { User, MediaAsset, Group } from "@/lib/types";
import { supabaseQueries } from "@/lib/supabase/queries";

import { DashboardLayout } from "@/components/dashboard-layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { LoadingSpinner } from "@/components/loading-states";
import { MediaCard } from "@/components/media-card";

interface ProfileContentProps {
  user: User;
  assets: MediaAsset[];
  friends: User[];
  groups: Group[];
}

export function ProfileContent({ user, assets, friends, groups }: ProfileContentProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [editedUser, setEditedUser] = useState({
    username: user.username || "",
    display_name: user.display_name || "",
    avatar_url: user.avatar_url || ""
  });

  const handleSaveProfile = async () => {
    setIsSaving(true);
    try {
      await supabaseQueries.updateUserProfile(user.id, editedUser);
      toast.success("Profile updated successfully!");
      setIsEditing(false);
      // In a real app, you'd refresh the user data here
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to update profile");
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancelEdit = () => {
    setEditedUser({
      username: user.username || "",
      display_name: user.display_name || "",
      avatar_url: user.avatar_url || ""
    });
    setIsEditing(false);
  };

  const stats = [
    { label: "Assets", value: assets.length, icon: Image },
    { label: "Friends", value: friends.length, icon: Users },
    { label: "Groups", value: groups.length, icon: Folder },
  ];

  const recentAssets = assets.slice(0, 6);

  return (
    <DashboardLayout user={user}>
      <div className="container mx-auto p-6 space-y-6">
        {/* Profile Header */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6">
              {/* Avatar */}
              <div className="relative">
                <Avatar className="h-24 w-24">
                  <AvatarImage src={editedUser.avatar_url || user.avatar_url} />
                  <AvatarFallback className="text-2xl">
                    {editedUser.display_name?.[0] || editedUser.username?.[0] || user.email[0]}
                  </AvatarFallback>
                </Avatar>
                {isEditing && (
                  <Button
                    size="icon"
                    variant="secondary"
                    className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full"
                  >
                    <Camera className="h-4 w-4" />
                  </Button>
                )}
              </div>

              {/* Profile Info */}
              <div className="flex-1 space-y-2">
                {isEditing ? (
                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="display-name">Display Name</Label>
                      <Input
                        id="display-name"
                        value={editedUser.display_name}
                        onChange={(e) => setEditedUser({ ...editedUser, display_name: e.target.value })}
                        placeholder="Your display name"
                      />
                    </div>
                    <div>
                      <Label htmlFor="username">Username</Label>
                      <Input
                        id="username"
                        value={editedUser.username}
                        onChange={(e) => setEditedUser({ ...editedUser, username: e.target.value })}
                        placeholder="Your username"
                      />
                    </div>
                  </div>
                ) : (
                  <>
                    <h1 className="text-3xl font-bold">
                      {user.display_name || user.username || "Anonymous User"}
                    </h1>
                    {user.username && (
                      <p className="text-muted-foreground">@{user.username}</p>
                    )}
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <Mail className="h-4 w-4" />
                        <span>{user.email}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>Joined {new Date(user.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-2">
                {isEditing ? (
                  <>
                    <Button onClick={handleSaveProfile} disabled={isSaving}>
                      {isSaving ? (
                        <LoadingSpinner size="sm" className="mr-2" />
                      ) : (
                        <Save className="h-4 w-4 mr-2" />
                      )}
                      Save
                    </Button>
                    <Button variant="outline" onClick={handleCancelEdit}>
                      <X className="h-4 w-4 mr-2" />
                      Cancel
                    </Button>
                  </>
                ) : (
                  <Button onClick={() => setIsEditing(true)}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Profile
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {stats.map((stat) => (
            <Card key={stat.label}>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-primary/10 rounded-lg">
                    <stat.icon className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold">{stat.value}</p>
                    <p className="text-muted-foreground">{stat.label}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Profile Tabs */}
        <Tabs defaultValue="assets" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="assets">Recent Assets</TabsTrigger>
            <TabsTrigger value="friends">Friends</TabsTrigger>
            <TabsTrigger value="groups">Groups</TabsTrigger>
          </TabsList>

          <TabsContent value="assets" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Recent Assets</CardTitle>
                <CardDescription>
                  Your latest uploaded media assets
                </CardDescription>
              </CardHeader>
              <CardContent>
                {recentAssets.length > 0 ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                    {recentAssets.map((asset) => (
                      <MediaCard
                        key={asset.id}
                        asset={asset}
                        className="w-full"
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Image className="h-12 w-12 mx-auto mb-4" />
                    <p>No assets uploaded yet</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="friends" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Friends ({friends.length})</CardTitle>
                <CardDescription>
                  Your network connections
                </CardDescription>
              </CardHeader>
              <CardContent>
                {friends.length > 0 ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                    {friends.map((friend) => (
                      <div key={friend.id} className="flex items-center space-x-3 p-3 rounded-lg border">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={friend.avatar_url} />
                          <AvatarFallback>
                            {friend.display_name?.[0] || friend.username?.[0] || friend.email[0]}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium truncate">
                            {friend.display_name || friend.username || "Anonymous"}
                          </p>
                          <p className="text-sm text-muted-foreground truncate">
                            {friend.email}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Users className="h-12 w-12 mx-auto mb-4" />
                    <p>No friends added yet</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="groups" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Groups ({groups.length})</CardTitle>
                <CardDescription>
                  Collaborative workspaces you're part of
                </CardDescription>
              </CardHeader>
              <CardContent>
                {groups.length > 0 ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                    {groups.map((group) => (
                      <div key={group.id} className="flex items-center space-x-3 p-3 rounded-lg border">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={group.avatar_url} />
                          <AvatarFallback>
                            {group.name[0]?.toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium truncate">{group.name}</p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge variant={group.is_private ? "secondary" : "outline"} className="text-xs">
                              {group.is_private ? "Private" : "Public"}
                            </Badge>
                            {group.owner_id === user.id && (
                              <Badge variant="default" className="text-xs">
                                Owner
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Folder className="h-12 w-12 mx-auto mb-4" />
                    <p>No groups joined yet</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
