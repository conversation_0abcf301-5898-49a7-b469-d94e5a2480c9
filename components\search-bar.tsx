"use client";

import { useState, useEffect } from "react";
import { Search, X, Filter } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  onTagsChange?: (tags: string[]) => void;
  selectedTags?: string[];
  availableTags?: string[];
  placeholder?: string;
  className?: string;
}

export function SearchBar({
  value,
  onChange,
  onTagsChange,
  selectedTags = [],
  availableTags = [],
  placeholder = "Search assets...",
  className,
}: SearchBarProps) {
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [tagInput, setTagInput] = useState("");

  const handleTagAdd = (tag: string) => {
    if (tag && !selectedTags.includes(tag)) {
      onTagsChange?.([...selectedTags, tag]);
    }
    setTagInput("");
  };

  const handleTagRemove = (tagToRemove: string) => {
    onTagsChange?.(selectedTags.filter(tag => tag !== tagToRemove));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && tagInput.trim()) {
      e.preventDefault();
      handleTagAdd(tagInput.trim());
    }
  };

  const filteredAvailableTags = availableTags.filter(
    tag => 
      tag.toLowerCase().includes(tagInput.toLowerCase()) &&
      !selectedTags.includes(tag)
  );

  const hasActiveFilters = selectedTags.length > 0;

  return (
    <div className={cn("space-y-3", className)}>
      {/* Main Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className="pl-10 pr-20 h-12 bg-background/50 backdrop-blur-sm border-border/50 focus:border-border"
        />
        <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center space-x-1">
          {value && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => onChange("")}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
          
          <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className={cn(
                  "h-8 w-8",
                  hasActiveFilters && "text-primary bg-primary/10"
                )}
              >
                <Filter className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-4" align="end">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Filter by Tags</h4>
                  {hasActiveFilters && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onTagsChange?.([])}
                      className="h-auto p-1 text-xs"
                    >
                      Clear all
                    </Button>
                  )}
                </div>

                {/* Tag Input */}
                <div className="space-y-2">
                  <Input
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder="Add tag..."
                    className="h-9"
                  />
                  
                  {/* Available Tags */}
                  {filteredAvailableTags.length > 0 && (
                    <div className="space-y-2">
                      <p className="text-xs text-muted-foreground">
                        Available tags:
                      </p>
                      <div className="flex flex-wrap gap-1 max-h-32 overflow-y-auto">
                        {filteredAvailableTags.slice(0, 20).map((tag) => (
                          <Badge
                            key={tag}
                            variant="outline"
                            className="cursor-pointer hover:bg-secondary text-xs"
                            onClick={() => handleTagAdd(tag)}
                          >
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Selected Tags */}
                {selectedTags.length > 0 && (
                  <>
                    <Separator />
                    <div className="space-y-2">
                      <p className="text-xs text-muted-foreground">
                        Active filters:
                      </p>
                      <div className="flex flex-wrap gap-1">
                        {selectedTags.map((tag) => (
                          <Badge
                            key={tag}
                            variant="secondary"
                            className="text-xs pr-1"
                          >
                            {tag}
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-4 w-4 ml-1 hover:bg-destructive hover:text-destructive-foreground"
                              onClick={() => handleTagRemove(tag)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {/* Active Filters Display */}
      {(hasActiveFilters || value) && (
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <span>Active filters:</span>
          {value && (
            <Badge variant="outline" className="text-xs">
              Search: "{value}"
            </Badge>
          )}
          {selectedTags.map((tag) => (
            <Badge key={tag} variant="outline" className="text-xs">
              Tag: {tag}
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
}
