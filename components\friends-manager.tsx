"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  UserPlus, 
  Users, 
  Search, 
  Check, 
  X, 
  MoreHorizontal,
  MessageCircle,
  UserMinus
} from "lucide-react";
import { toast } from "sonner";

import { User, Friendship } from "@/lib/types";
import { createClient } from "@/lib/supabase/client";
import { useUserStore } from "@/lib/store";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { EmptyState, LoadingSpinner } from "@/components/loading-states";

interface FriendsManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

export function FriendsManager({ isOpen, onClose }: FriendsManagerProps) {
  const { currentUser, friends, setFriends, addFriend, removeFriend } = useUserStore();
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [friendRequests, setFriendRequests] = useState<(Friendship & { requester: User })[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const supabase = createClient();

  useEffect(() => {
    if (isOpen) {
      loadFriends();
      loadFriendRequests();
    }
  }, [isOpen]);

  const loadFriends = async () => {
    if (!currentUser) return;

    try {
      const { data, error } = await supabase
        .from('friendships')
        .select(`
          *,
          requester:requester_id(id, username, display_name, avatar_url, email),
          addressee:addressee_id(id, username, display_name, avatar_url, email)
        `)
        .eq('status', 'accepted')
        .or(`requester_id.eq.${currentUser.id},addressee_id.eq.${currentUser.id}`);

      if (error) throw error;

      const friendsList = data.map((friendship: any) => {
        return friendship.requester_id === currentUser.id 
          ? friendship.addressee 
          : friendship.requester;
      });

      setFriends(friendsList);
    } catch (error) {
      console.error('Error loading friends:', error);
    }
  };

  const loadFriendRequests = async () => {
    if (!currentUser) return;

    try {
      const { data, error } = await supabase
        .from('friendships')
        .select(`
          *,
          requester:requester_id(id, username, display_name, avatar_url, email)
        `)
        .eq('addressee_id', currentUser.id)
        .eq('status', 'pending');

      if (error) throw error;

      setFriendRequests(data || []);
    } catch (error) {
      console.error('Error loading friend requests:', error);
    }
  };

  const searchUsers = async () => {
    if (!searchQuery.trim() || !currentUser) return;

    setIsSearching(true);
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, username, display_name, avatar_url, email')
        .neq('id', currentUser.id)
        .or(`username.ilike.%${searchQuery}%,display_name.ilike.%${searchQuery}%,email.ilike.%${searchQuery}%`)
        .limit(10);

      if (error) throw error;

      // Filter out existing friends and pending requests
      const existingFriendIds = friends.map(f => f.id);
      const pendingRequestIds = friendRequests.map(r => r.requester.id);
      
      const filteredResults = (data || []).filter(user => 
        !existingFriendIds.includes(user.id) && 
        !pendingRequestIds.includes(user.id)
      );

      setSearchResults(filteredResults);
    } catch (error) {
      toast.error("Failed to search users");
      console.error('Search error:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const sendFriendRequest = async (userId: string) => {
    if (!currentUser) return;

    try {
      const { error } = await supabase
        .from('friendships')
        .insert({
          requester_id: currentUser.id,
          addressee_id: userId,
          status: 'pending'
        });

      if (error) throw error;

      toast.success("Friend request sent!");
      setSearchResults(prev => prev.filter(user => user.id !== userId));
    } catch (error) {
      toast.error("Failed to send friend request");
      console.error('Friend request error:', error);
    }
  };

  const respondToFriendRequest = async (requestId: string, accept: boolean) => {
    try {
      if (accept) {
        const { error } = await supabase
          .from('friendships')
          .update({ status: 'accepted' })
          .eq('id', requestId);

        if (error) throw error;

        toast.success("Friend request accepted!");
        loadFriends();
      } else {
        const { error } = await supabase
          .from('friendships')
          .delete()
          .eq('id', requestId);

        if (error) throw error;

        toast.success("Friend request declined");
      }

      loadFriendRequests();
    } catch (error) {
      toast.error("Failed to respond to friend request");
      console.error('Friend request response error:', error);
    }
  };

  const removeFriendship = async (friendId: string) => {
    if (!currentUser) return;

    try {
      const { error } = await supabase
        .from('friendships')
        .delete()
        .or(`and(requester_id.eq.${currentUser.id},addressee_id.eq.${friendId}),and(requester_id.eq.${friendId},addressee_id.eq.${currentUser.id})`);

      if (error) throw error;

      removeFriend(friendId);
      toast.success("Friend removed");
    } catch (error) {
      toast.error("Failed to remove friend");
      console.error('Remove friend error:', error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Friends & Connections</span>
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="friends" className="flex-1">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="friends">
              Friends ({friends.length})
            </TabsTrigger>
            <TabsTrigger value="requests">
              Requests ({friendRequests.length})
            </TabsTrigger>
            <TabsTrigger value="search">
              Find Friends
            </TabsTrigger>
          </TabsList>

          <TabsContent value="friends" className="space-y-4 max-h-96 overflow-y-auto">
            {friends.length === 0 ? (
              <EmptyState
                icon={<Users className="h-8 w-8" />}
                title="No friends yet"
                description="Start connecting with other users to build your network."
              />
            ) : (
              <div className="space-y-2">
                {friends.map((friend) => (
                  <motion.div
                    key={friend.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex items-center justify-between p-3 rounded-lg border"
                  >
                    <div className="flex items-center space-x-3">
                      <Avatar>
                        <AvatarImage src={friend.avatar_url} />
                        <AvatarFallback>
                          {friend.display_name?.[0] || friend.email[0].toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">
                          {friend.display_name || friend.email}
                        </p>
                        {friend.username && (
                          <p className="text-sm text-muted-foreground">
                            @{friend.username}
                          </p>
                        )}
                      </div>
                    </div>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <MessageCircle className="mr-2 h-4 w-4" />
                          Message
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => removeFriendship(friend.id)}
                          className="text-destructive"
                        >
                          <UserMinus className="mr-2 h-4 w-4" />
                          Remove Friend
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </motion.div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="requests" className="space-y-4 max-h-96 overflow-y-auto">
            {friendRequests.length === 0 ? (
              <EmptyState
                icon={<UserPlus className="h-8 w-8" />}
                title="No pending requests"
                description="You don't have any pending friend requests."
              />
            ) : (
              <div className="space-y-2">
                {friendRequests.map((request) => (
                  <motion.div
                    key={request.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex items-center justify-between p-3 rounded-lg border"
                  >
                    <div className="flex items-center space-x-3">
                      <Avatar>
                        <AvatarImage src={request.requester.avatar_url} />
                        <AvatarFallback>
                          {request.requester.display_name?.[0] || request.requester.email[0].toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">
                          {request.requester.display_name || request.requester.email}
                        </p>
                        {request.requester.username && (
                          <p className="text-sm text-muted-foreground">
                            @{request.requester.username}
                          </p>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        onClick={() => respondToFriendRequest(request.id, true)}
                      >
                        <Check className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => respondToFriendRequest(request.id, false)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="search" className="space-y-4">
            <div className="flex space-x-2">
              <Input
                placeholder="Search by username, name, or email..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && searchUsers()}
              />
              <Button onClick={searchUsers} disabled={isSearching}>
                {isSearching ? <LoadingSpinner size="sm" /> : <Search className="h-4 w-4" />}
              </Button>
            </div>

            <div className="max-h-80 overflow-y-auto space-y-2">
              {searchResults.map((user) => (
                <motion.div
                  key={user.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex items-center justify-between p-3 rounded-lg border"
                >
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarImage src={user.avatar_url} />
                      <AvatarFallback>
                        {user.display_name?.[0] || user.email[0].toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">
                        {user.display_name || user.email}
                      </p>
                      {user.username && (
                        <p className="text-sm text-muted-foreground">
                          @{user.username}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  <Button
                    size="sm"
                    onClick={() => sendFriendRequest(user.id)}
                  >
                    <UserPlus className="h-4 w-4 mr-2" />
                    Add Friend
                  </Button>
                </motion.div>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
