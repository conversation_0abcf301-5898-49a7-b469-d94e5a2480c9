"use client";

import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { UserPlus, Users, Clock, Search, MoreHorizontal, MessageCircle, UserMinus } from "lucide-react";
import { toast } from "sonner";

import { User, <PERSON> } from "@/lib/types";
import { useUserStore } from "@/lib/store";
import { supabaseQueries } from "@/lib/supabase/queries";

import { DashboardLayout } from "@/components/dashboard-layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { LoadingSpinner, EmptyState } from "@/components/loading-states";

interface FriendsContentProps {
  user: User;
  initialFriends: User[];
  initialPendingRequests: Friendship[];
}

export function FriendsContent({ user, initialFriends, initialPendingRequests }: FriendsContentProps) {
  const [friends, setFriends] = useState<User[]>(initialFriends);
  const [pendingRequests, setPendingRequests] = useState<Friendship[]>(initialPendingRequests);
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [addFriendEmail, setAddFriendEmail] = useState("");
  const [isAddingFriend, setIsAddingFriend] = useState(false);

  const { setCurrentUser, setFriends: setStoreFriends } = useUserStore();

  useEffect(() => {
    setCurrentUser(user);
    setStoreFriends(friends);
  }, [user, friends, setCurrentUser, setStoreFriends]);

  const filteredFriends = friends.filter(friend =>
    friend.display_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    friend.username?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    friend.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSendFriendRequest = async () => {
    if (!addFriendEmail.trim()) {
      toast.error("Please enter an email address");
      return;
    }

    setIsAddingFriend(true);
    try {
      await supabaseQueries.sendFriendRequest(addFriendEmail);
      toast.success("Friend request sent!");
      setAddFriendEmail("");
    } catch (error) {
      console.error("Error sending friend request:", error);
      toast.error("Failed to send friend request");
    } finally {
      setIsAddingFriend(false);
    }
  };

  const handleAcceptRequest = async (requestId: string) => {
    try {
      await supabaseQueries.acceptFriendRequest(requestId);
      toast.success("Friend request accepted!");
      
      // Refresh data
      const [newFriends, newRequests] = await Promise.all([
        supabaseQueries.getUserFriends(user.id),
        supabaseQueries.getPendingFriendRequests(user.id)
      ]);
      
      setFriends(newFriends);
      setPendingRequests(newRequests);
    } catch (error) {
      console.error("Error accepting friend request:", error);
      toast.error("Failed to accept friend request");
    }
  };

  const handleDeclineRequest = async (requestId: string) => {
    try {
      await supabaseQueries.declineFriendRequest(requestId);
      toast.success("Friend request declined");
      
      const newRequests = await supabaseQueries.getPendingFriendRequests(user.id);
      setPendingRequests(newRequests);
    } catch (error) {
      console.error("Error declining friend request:", error);
      toast.error("Failed to decline friend request");
    }
  };

  const handleRemoveFriend = async (friendId: string) => {
    try {
      await supabaseQueries.removeFriend(friendId);
      toast.success("Friend removed");
      
      const newFriends = await supabaseQueries.getUserFriends(user.id);
      setFriends(newFriends);
    } catch (error) {
      console.error("Error removing friend:", error);
      toast.error("Failed to remove friend");
    }
  };

  return (
    <DashboardLayout user={user}>
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Friends</h1>
            <p className="text-muted-foreground">
              Manage your connections and collaborate with others
            </p>
          </div>
          
          <Dialog>
            <DialogTrigger asChild>
              <Button>
                <UserPlus className="h-4 w-4 mr-2" />
                Add Friend
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add Friend</DialogTitle>
                <DialogDescription>
                  Send a friend request by entering their email address
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <Input
                  placeholder="Enter email address"
                  value={addFriendEmail}
                  onChange={(e) => setAddFriendEmail(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && handleSendFriendRequest()}
                />
                <Button 
                  onClick={handleSendFriendRequest}
                  disabled={isAddingFriend}
                  className="w-full"
                >
                  {isAddingFriend ? (
                    <LoadingSpinner size="sm" className="mr-2" />
                  ) : (
                    <UserPlus className="h-4 w-4 mr-2" />
                  )}
                  Send Request
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        <Tabs defaultValue="friends" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="friends" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Friends ({friends.length})
            </TabsTrigger>
            <TabsTrigger value="requests" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Requests ({pendingRequests.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="friends" className="space-y-6">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search friends..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Friends Grid */}
            {filteredFriends.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <AnimatePresence>
                  {filteredFriends.map((friend) => (
                    <FriendCard
                      key={friend.id}
                      friend={friend}
                      onRemove={() => handleRemoveFriend(friend.id)}
                    />
                  ))}
                </AnimatePresence>
              </div>
            ) : (
              <EmptyState
                icon={<Users className="h-12 w-12" />}
                title={searchQuery ? "No friends found" : "No friends yet"}
                description={
                  searchQuery 
                    ? "Try adjusting your search terms"
                    : "Start building your network by adding friends"
                }
              />
            )}
          </TabsContent>

          <TabsContent value="requests" className="space-y-6">
            {pendingRequests.length > 0 ? (
              <div className="space-y-4">
                {pendingRequests.map((request) => (
                  <FriendRequestCard
                    key={request.id}
                    request={request}
                    onAccept={() => handleAcceptRequest(request.id)}
                    onDecline={() => handleDeclineRequest(request.id)}
                  />
                ))}
              </div>
            ) : (
              <EmptyState
                icon={<Clock className="h-12 w-12" />}
                title="No pending requests"
                description="You don't have any pending friend requests"
              />
            )}
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}

interface FriendCardProps {
  friend: User;
  onRemove: () => void;
}

function FriendCard({ friend, onRemove }: FriendCardProps) {
  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      transition={{ duration: 0.2 }}
    >
      <Card className="group hover:shadow-lg transition-all duration-200">
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <Avatar className="h-12 w-12">
              <AvatarImage src={friend.avatar_url} />
              <AvatarFallback>
                {friend.display_name?.[0] || friend.username?.[0] || friend.email[0]}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold truncate">
                {friend.display_name || friend.username || "Anonymous"}
              </h3>
              <p className="text-sm text-muted-foreground truncate">
                {friend.email}
              </p>
              {friend.username && (
                <Badge variant="secondary" className="mt-1">
                  @{friend.username}
                </Badge>
              )}
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100 transition-opacity">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Send Message
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={onRemove} className="text-destructive">
                  <UserMinus className="h-4 w-4 mr-2" />
                  Remove Friend
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

interface FriendRequestCardProps {
  request: Friendship;
  onAccept: () => void;
  onDecline: () => void;
}

function FriendRequestCard({ request, onAccept, onDecline }: FriendRequestCardProps) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Avatar className="h-10 w-10">
              <AvatarFallback>
                {request.requester_id[0]}
              </AvatarFallback>
            </Avatar>
            <div>
              <h3 className="font-semibold">Friend Request</h3>
              <p className="text-sm text-muted-foreground">
                From: {request.requester_id}
              </p>
            </div>
          </div>
          
          <div className="flex space-x-2">
            <Button size="sm" onClick={onAccept}>
              Accept
            </Button>
            <Button size="sm" variant="outline" onClick={onDecline}>
              Decline
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
